/**
 * Core timer calculation logic shared between server and client
 */
import { DEFAULT_DURATION } from './constants.js';

/**
 * Timer core class for managing timer state and calculations
 */
class TimerCore {
  /**
   * Create a new TimerCore instance.
   * @param {Array} [intervals=[]] - Array of interval objects, each with a duration property in seconds.
   */
  constructor(intervals = []) {
    this.intervals = intervals;
    this.state = {
      // Shared timer state
      repeat: false,
      currentInterval: 0,
      timeRemaining: (intervals[0]?.duration ?? DEFAULT_DURATION) * 1000,
      isRunning: false,
      isPaused: false,

      // Internal timing state
      startedInterval: 0,
      startedAt: 0,
      pausedAt: 0,
      timePaused: 0,
    };
  }

  /**
   * Start the timer. If paused, resumes from pause; otherwise, starts from the current interval.
   * @returns {Object} Updated timer state object.
   */
  start() {
    const timer = this.state;

    // Handle resume from pause
    if (timer.isPaused) {
      this.resume();
    } else if (!timer.isRunning) {
      timer.startedInterval = timer.currentInterval;
      timer.startedAt = Date.now();
      timer.timePaused = 0;
    }

    // Update timer state
    timer.isRunning = true;
    timer.isPaused = false;
    timer.pausedAt = 0;

    return { ...timer };
  }

  /**
   * Pause the timer at the current time.
   * @returns {Object} Updated timer state object.
   */
  pause() {
    const timer = this.state;

    // Update timer state
    timer.isPaused = true;
    timer.pausedAt = Date.now();

    return { ...timer };
  }

  /**
   * Stop the timer and reset to the initial state and first interval.
   * @returns {Object} Updated timer state object.
   */
  stop() {
    const timer = this.state;

    // Reset timer state
    timer.isRunning = false;
    timer.isPaused = false;
    timer.currentInterval = 0;
    timer.timeRemaining = (this.intervals[0]?.duration ?? DEFAULT_DURATION) * 1000;
    timer.startedInterval = 0;
    timer.startedAt = 0;
    timer.pausedAt = 0;
    timer.timePaused = 0;

    return { ...timer };
  }

  /**
   * Toggle repeat mode or set it explicitly.
   * @param {boolean} [repeat] - If provided, sets repeat mode to this value; otherwise, toggles current repeat mode.
   * @returns {Object} Updated timer state object.
   */
  repeat(repeat = null) {
    const timer = this.state;
    timer.repeat = Boolean(repeat ?? !timer.repeat);
    return { ...timer };
  }

  /**
   * Move to the next interval. Wraps to the first interval if at the end.
   * @returns {Object} Updated timer state object.
   */
  next() {
    const timer = this.state;
    const now = Date.now();

    if (++timer.currentInterval >= this.intervals.length) {
      timer.currentInterval = 0;
    }

    timer.timeRemaining = (this.intervals[timer.currentInterval]?.duration ?? DEFAULT_DURATION) * 1000;

    if (timer.isRunning) {
      timer.startedInterval = timer.currentInterval;
      timer.startedAt = now;
      timer.pausedAt = timer.isPaused ? now : 0;
      timer.timePaused = 0;
    }

    return { ...timer };
  }

  /**
   * Resume the timer from a paused state.
   * @returns {Object} Updated timer state object.
   */
  resume() {
    const now = Date.now();
    const timer = this.state;

    if (timer.isPaused) {
      // Add pause duration to total paused time
      timer.isPaused = false;
      timer.timePaused += now - timer.pausedAt;
      timer.pausedAt = 0;
    }

    return { ...timer };
  }

  /**
   * Synchronize timer state based on elapsed time and interval durations.
   * @returns {Object} Updated timer state object.
   */
  sync() {
    const now = Date.now();
    const timer = this.state;

    // Validate timer state
    if (!timer.isRunning || !timer.startedAt) return { ...timer };

    // Validate intervals
    const intervals = this.intervals;
    if (!intervals || intervals.length === 0) return { ...timer };

    // Calculate actual elapsed time
    const offset = timer.isPaused && timer.pausedAt > 0 ? now - timer.pausedAt : 0;
    const elapsed = now - timer.startedAt - timer.timePaused - offset;

    // Find current interval starting from startedInterval
    let current = timer.startedInterval;
    if (current >= intervals.length) current = 0;

    // Calculate remaining time in the `current` interval
    let remaining = elapsed;
    while (remaining > 0) {
      const duration = intervals[current].duration * 1000;
      if (remaining < duration) break;

      remaining -= duration;
      current++;

      // Handle repeat mode wraparound
      if (current >= intervals.length && timer.repeat) {
        current = 0;
      }

      // Check if timer should stop (no repeat and reached end)
      if (current >= intervals.length && !timer.repeat) {
        timer.isRunning = false;
        timer.isPaused = false;
        timer.currentInterval = 0;
        timer.timeRemaining = intervals[0].duration * 1000;
        timer.startedAt = 0;
        timer.startedInterval = 0;
        timer.pausedAt = 0;
        timer.timePaused = 0;
        return { ...timer };
      }
    }

    // Update session state
    timer.currentInterval = current;
    timer.timeRemaining = intervals[current].duration * 1000 - remaining;

    return { ...timer };
  }

  /**
   * Update timer state from an external source (e.g., server or another client).
   * @param {Object} state - Timer state object from external source.
   * @returns {Object} Updated timer state object.
   */
  updateFromSource(state) {
    const timer = this.state;
    const now = Date.now();

    // Update timer state from source
    timer.repeat = state.repeat;
    timer.currentInterval = state.currentInterval;
    timer.timeRemaining = state.timeRemaining;
    timer.isRunning = state.isRunning;
    timer.isPaused = state.isPaused;

    // Calculate elapsed time
    const duration = (this.intervals[timer.currentInterval]?.duration ?? DEFAULT_DURATION) * 1000;
    const elapsed = duration - timer.timeRemaining;

    // Reset timer baseline
    timer.startedInterval = timer.currentInterval;
    timer.startedAt = timer.isRunning ? now - elapsed : 0; // Calculate when this interval started by subtracting elapsed time from current time
    timer.pausedAt = timer.isPaused ? now : 0;
    timer.timePaused = 0;

    return { ...timer };
  }

  /**
   * Update the intervals array and adjust timer state if necessary.
   * @param {Array} intervals - Array of interval objects, each with a duration property in seconds.
   */
  updateIntervals(intervals) {
    this.intervals = intervals;

    const now = Date.now();
    const timer = this.state;

    // Last interval deleted, reset to first interval
    if (timer.currentInterval >= intervals.length) {
      timer.timeRemaining = (intervals[0]?.duration ?? DEFAULT_DURATION) * 1000;
      timer.currentInterval = 0;
      timer.startedInterval = 0;
      timer.startedAt = timer.startedAt ? now : 0;
      timer.pausedAt = timer.pausedAt ? now : 0;
      timer.timePaused = 0;
      return;
    }

    const duration = (intervals[timer.currentInterval]?.duration ?? DEFAULT_DURATION) * 1000;

    if (timer.isRunning) {
      const elapsed = now - timer.startedAt - timer.timePaused;
      timer.startedAt = now - elapsed;
      timer.startedInterval = timer.currentInterval;
      timer.timePaused = 0;
      timer.pausedAt = timer.isPaused ? now : 0;

      if (timer.timeRemaining > duration) {
        timer.timeRemaining = duration;
        timer.startedAt = now;
      }

      return;
    }

    timer.timeRemaining = duration;
  }

  /**
   * Get a copy of the current timer state.
   * @returns {Object} Current timer state object.
   */
  getState() {
    return { ...this.state };
  }

  /**
   * Set the timer state for initialization or restoration.
   * @param {Object} state - Timer state object to merge into current state.
   */
  setState(state) {
    this.state = { ...this.state, ...state };
  }
}

export default TimerCore;

// Browser export
if (typeof window !== 'undefined') {
  window.TimerCore = TimerCore;
}
