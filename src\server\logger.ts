/**
 * Logger configuration using Pino
 */

import type { Logger, TransportSingleOptions } from 'pino';
import pino from 'pino';

/**
 * Create and configure the logger instance based on environment.
 *
 * @returns configured pino logger instance.
 */
export function createLogger(serviceName?: string): Logger {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const logLevel = process.env.LOG_LEVEL || 'info';

  const transport: TransportSingleOptions = {
    target: 'pino-pretty',
    options: {
      translateTime: true,
      hideObject: !isDevelopment,
    },
  };

  const logger = pino({
    name: serviceName || 'server',
    level: logLevel,
    transport,
    base: {
      pid: process.pid,
      hostname: process.env.HOSTNAME || 'localhost',
    },
  });

  return logger;
}

export const logger = createLogger();
export default logger;
