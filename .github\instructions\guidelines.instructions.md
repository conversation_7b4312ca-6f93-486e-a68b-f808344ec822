---
applyTo: '**'
---

# Agent Guidelines

General guidelines for coding agents to follow when executing tasks.

## Formatting

- ALWAYS use Unix-style line endings (LF).
- NEVER use Windows-style line endings (CRLF).
- NEVER use Mac-style line endings (CR).
- ALWAYS use UTF-8 encoding for all files.
- ALWAYS use spaces for indentation (2 spaces).
- NEVER use tabs for indentation.
- ALWAYS ensure consistent formatting throughout the file.

## Coding

- DO NOT add any functions or methods that will go unused.
- DO NOT add any variables that are not needed to complete the task.
- DO NOT add code that is not directly related to the task at hand.
- DO NOT add code that is not explicitly requested.
- DO NOT include extraneous or unnecessary code.
- DO NOT overcomplicate solutions; prefer simple, direct implementations.
- DO NOT introduce unnecessary complexity into the code.
- REMOVE all unused code upon task completion.
- REMOVE any unnecessary or redundant code fragments.
- ENSURE all code is relevant to the plan and requirements.

### Naming

- ALWAYS use short, concise, but meaningful variable and function names.
- PREFER single word variable names where possible.
- NEVER use abbreviations in variable or function names.
- NEVER use single-character variable names, except for loop counters.

### Refactoring

- REMOVE all old or unused code when refactoring.
