# Contributing Guidelines

This document provides guidelines and instructions for developing and contributing to the Fycus application.

## Philosophy

Fycus is designed to be lightweight and permissionless, featuring a clean and simple user interface that allows teams to easily synchronize their work and break intervals. It does not require any complex setup, user authentication, or database management. The goal is to make it as frictionless as possible, with minimal barriers to entry.

## Technology

Fycus is built using the following tech stack:

- **Frontend**: Vanilla JavaScript, HTML, CSS, JSON
- **Backend**: TypeScript, Node.js, Express.js, WebSocket
- **Testing**: Jest, Playwright

### Server

Fycus server is responsible for managing real-time communication between clients, relaying session data, calculating timer state, and exposing endpoints for session management.

- Uses WebSockets for real-time updates
- Calculates timer state when requested by clients
- Does not require a database or user authentication
- Holds session data in memory for a configurable period after the last client disconnects

## Development

To set up the development environment for Fycus, follow these steps:

1. Clone the repository:

```bash
git clone https://github.com/kapdap/fycus.git
cd fycus
```

2. Install the dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

The development server will be available at http://localhost:3000.

## Building

1. To build the application, run:

```bash
npm run build
```

This will compile the TypeScript code and bundle the public assets for production use in the `dist` directory.

2. To start the production server, run:

```bash
npm start
```

The production server will be available at http://localhost:3000.

## Testing

To run the unit tests with Jest, use the following command:

```bash
npm test
```

To run the end-to-end tests with Playwright, use the following command:

```bash
npx playwright test
```

End-to-end tests will run against the application running on http://localhost:3000, so ensure the server is running before executing the tests.

_TODO: Start a new server instance for Playwright tests to avoid conflicts with the development server._

## Libraries

### Frontend

- **[jshashes](https://github.com/h2non/jshashes)**: Hashing and checksum functions - using sha256 for gravatar email hashing
- **[NoSleep.js](https://github.com/richtr/NoSleep.js/)**: Prevents mobile devices from going to sleep
- **[qr-creator](https://github.com/nimiq/qr-creator)**: Generates QR code in session sharing panel

## Generative AI

You are welcome to use Generative AI tools to assist with the development of Fycus; however, you are expected to have a complete understanding of all code that you contribute to the project, as well as the tools used to generate it. Please be sure to carefully review, critically analyze, and test AI generated code prior to contributing it.

Keep in mind that code created by Generative AI can often be verbose and may introduce unnecessary complexity, references to non-existent code or libraries ("hallucinations"), and other potential issues. It is your responsibility to thoroughly review, test, and refactor any AI generated code that you contribute to ensure it is fully functional, aligns with project standards, and adheres to all licensing requirements.

Please note that we reserve the right to reject any pull requests that do not meet the project's standards.

### Acceptable Use

Examples of acceptable use of Generative AI tools:

- Planning and outlining
- Research and brainstorming
- Understanding the codebase
- Improving JSDoc/TypeDoc readability
- Improving documentation readability

## License

All contributions to Fycus are governed by the GPL-3.0 license. By contributing, you agree to license your contributions under the same terms.
