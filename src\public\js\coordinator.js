import Utils, { Events, Storage } from './utils.js';

/**
 * Coordinator class for managing WebSocket events and component updates
 * Acts as the single listener for all WebSocket events and coordinates component updates
 * TODO: Move all functionality in this class to app.js
 */
class Coordinator {
  /**
   * Create a Coordinator instance.
   * @param {Object} app - Main application instance
   */
  constructor(app) {
    this.app = app;

    this.components = new Map();
    this.eventHandlers = new Map();

    this._initialize();
  }

  /**
   * Set up event coordination system.
   */
  _initialize() {
    this._listeners();
  }

  /**
   * Register listeners for all WebSocket events.
   */
  _listeners() {
    Events.on(document, 'sessionCreated', this._processEvent.bind(this));
    Events.on(document, 'sessionJoined', this._processEvent.bind(this));
    Events.on(document, 'intervalsUpdated', this._processEvent.bind(this));
    Events.on(document, 'timerUpdated', this._processEvent.bind(this));
    Events.on(document, 'userConnected', this._processEvent.bind(this));
    Events.on(document, 'userDisconnected', this._processEvent.bind(this));
    Events.on(document, 'userUpdated', this._processEvent.bind(this));
    Events.on(document, 'connectedUsers', this._processEvent.bind(this));
    Events.on(document, 'websocketConnected', this._processEvent.bind(this));
    Events.on(document, 'websocketDisconnected', this._processEvent.bind(this));
  }

  /**
   * Register application components for event coordination.
   */
  _components() {
    if (this.components.size > 0) return;

    this._register('settings', this.app.settings, ['sessionCreated', 'intervalsUpdated', 'timerUpdated']);
    this._register('intervals', this.app.intervals, ['sessionCreated', 'intervalsUpdated']);
    this._register('sessions', this.app.sessions, [
      'sessionCreated',
      'sessionJoined',
      'userConnected',
      'userDisconnected',
      'userUpdated',
      'connectedUsers',
    ]);
    this._register('timer', this.app.timer, ['sessionCreated', 'sessionJoined', 'intervalsUpdated', 'timerUpdated']);
    this._register('user', this.app.user, [
      'sessionCreated',
      'sessionJoined',
      'userConnected',
      'userDisconnected',
      'userUpdated',
    ]);

    console.log('Event coordinator components initialized');
  }

  /**
   * Add component to event coordination system.
   * @param {string} name - Component name.
   * @param {Object} component - Component instance with render method.
   * @param {string[]} events - Array of event types this component should handle.
   */
  _register(name, component, events) {
    this.components.set(name, {
      instance: component,
      events: new Set(events),
    });
  }

  /**
   * Coordinate event processing across all components.
   * @param {Object} event - Event object dispatched from the SocketClient. Must have 'type' and 'detail' properties.
   * @returns {Promise<void>} Resolves when event processing is complete.
   */
  async _processEvent(event) {
    const { type, detail: data } = event;

    try {
      await this.update(type, data);
      this.render(type);
    } catch (error) {
      console.error(`Error processing ${type} event:`, error);
    }
  }

  /**
   * Synchronize session state based on WebSocket events.
   * @param {string} type - WebSocket event type.
   * @param {Object} data - Event data payload.
   * @returns {Promise<void>} Resolves when state update is complete.
   */
  async update(type, data) {
    if (this.components.size === 0) {
      if (type === 'websocketConnected') {
        this._components();
      } else {
        return;
      }
    }

    if (!data?.sessionId) return;

    let session = Storage.getSession(data.sessionId);
    if (!session) {
      session = await Utils.createDefaultSession(data.sessionId);
    }

    switch (type) {
      case 'sessionCreated':
        session.sessionId = data.sessionId;

        session.user.clientId = data.clientId;
        session.user.hashedId = await Utils.getSHA256(data.clientId);
        session.user.avatarUrl = await Utils.getGravatarUrl(session.user.email || session.user.hashedId);

        if (this.app.timer.timerCore) {
          session.timer = this.app.timer.timerCore.sync();
        }

        Storage.saveSession(session);
        break;

      case 'sessionJoined':
        Object.assign(session, {
          ...data.session,
          timer: Object.assign(session.timer, data.session.timer),
        });

        if (session.user.clientId !== data.clientId) {
          session.user.clientId = data.clientId;
          session.user.hashedId = await Utils.getSHA256(data.clientId);
          session.user.avatarUrl = await Utils.getGravatarUrl(session.user.email || session.user.hashedId);
        }

        Storage.saveSession(session);
        break;

      case 'intervalsUpdated':
        if (!data.session) return;
        Object.assign(session, data.session);
        Storage.saveSession(session);
        break;

      case 'timerUpdated':
        if (!data.timer) return;
        Object.assign(session.timer, data.timer);
        Storage.saveSession(session);
        break;

      case 'userConnected':
      case 'userDisconnected':
      case 'userUpdated':
      case 'connectedUsers':
      case 'websocketConnected':
      case 'websocketDisconnected':
        // Doesn't modify session data
        break;

      default:
        console.warn(`Unknown event type for session data update: ${type}`);
    }

    await this.app.update(type, data, session);
  }

  /**
   * Render components
   * @param {string} type - WebSocket event type
   * @returns {Promise<void>} Resolves when all relevant components have been rendered
   */
  render(type) {
    for (const name of this.components.keys()) {
      const component = this.components.get(name);
      if (component && component.events.has(type)) {
        try {
          if (typeof component.instance?.render === 'function') {
            component.instance.render();
          }
        } catch (error) {
          console.error(`Error rendering component ${name} for ${type}:`, error);
        }
      }
    }
  }

  /**
   * Clean up registered components.
   */
  dispose() {
    this.components.clear();
  }
}

export default Coordinator;
