/**
 * Session management for the collaborative timer server (TypeScript)
 */

import type { Logger } from 'pino';
import type { ServerWebSocket } from '../types/server';
import type {
  IncomingMessage,
  JoinSessionMessage,
  TimerUpdateMessage,
  UserUpdateMessage,
  SessionInternal,
  UserInternal,
  OutgoingMessage,
  ErrorMessage,
  IntervalsUpdatedMessage,
  UserUpdated,
} from '../types/messages';
import {
  hashString,
  formatIncoming,
  formatClientId,
  formatSessionCreatedMsg,
  formatSessionJoinedMsg,
  formatUserConnectedMsg,
  formatUserDisconnectedMsg,
  formatUserUpdatedMsg,
  formatIntervalsUpdatedMsg,
  formatTimerUpdatedMsg,
  formatConnectedUsersMsg,
  formatInternalSession,
  formatInternalUser,
  formatErrorMsg,
} from './messages.js';
import { createLogger } from './logger';

/**
 * Session manager class for handling real-time collaboration.
 */
class SessionManager implements SessionManager {
  private logger: Logger;
  private sessions: Map<string, SessionInternal>;
  private handlers: Map<string, (ws: ServerWebSocket, message: IncomingMessage) => void>;
  private cleanup: NodeJS.Timeout | null;

  /**
   * Initializes the session manager, sets up message handlers, and starts the cleanup timer.
   */
  constructor() {
    this.logger = createLogger('session-manager');
    this.sessions = new Map();
    this.handlers = new Map();
    this.cleanup = null;

    this.setupHandlers();
    this.startCleanupTimer();
  }

  /**
   * Registers message handlers for supported message types.
   */
  private setupHandlers(): void {
    this.handlers.set('session_join', this.handleSessionJoin.bind(this));
    this.handlers.set('session_update', this.handleSessionUpdate.bind(this));
    this.handlers.set('timer_update', this.handleTimerUpdate.bind(this));
    this.handlers.set('user_update', this.handleUserUpdate.bind(this));
    this.handlers.set('user_list', this.handleUserList.bind(this));
  }

  /**
   * Handles an incoming WebSocket message and dispatches it to the appropriate handler.
   *
   * @param ws WebSocket connection for the client.
   * @param message The parsed incoming message object.
   */
  handleMessage(ws: ServerWebSocket, message: IncomingMessage): void {
    try {
      const parsed = formatIncoming(message) as IncomingMessage;
      const handler = this.handlers.get(parsed.type);
      if (handler) {
        handler(ws, parsed);
      } else {
        this.logger.warn({ messageType: parsed.type }, 'Unknown message type received');
        this.sendError(ws, 'Unknown message type');
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          {
            messageType: (message as { type?: string })?.type,
            error,
          },
          'Error handling message'
        );
        this.sendError(ws, `Failed to process ${(message as { type?: string })?.type}`);
      } else {
        this.logger.error('Unknown error type in handleMessage');
        this.sendError(ws, 'Unknown error');
      }
    }
  }

  /**
   * Handles a session join request from a client, creating or joining a session as needed.
   *
   * @param ws WebSocket connection for the client.
   * @param message The session join message.
   */
  private handleSessionJoin(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'session_join') return;
    const { sessionId, user } = message as JoinSessionMessage;

    ws.sessionId = sessionId;
    ws.clientId = formatClientId(user.clientId) as string;

    let session = this.getSession(ws.sessionId as string);
    const isNew = !session;

    if (isNew) session = this.createSession(ws.sessionId as string);
    if (!session) return;

    session.timer = session.timerInstance.sync();

    const now = Date.now();
    const clientId = ws.clientId as string;
    const existing = session.users[clientId];

    if (existing) {
      Object.assign(
        existing,
        formatInternalUser({
          ...user,
          clientId: existing.clientId,
          offlineAt: null,
          lastPing: now,
          ws,
        })
      );
      session.users[clientId] = existing;
      this.logger.info({ clientId, sessionId: ws.sessionId }, 'Client reconnected to session');

      const hasOffline = Array.from(Object.values(session.users)).some((u) => !u.ws || u.ws.readyState !== 1);
      if (!hasOffline && session.emptyAt) {
        session.emptyAt = null;
        this.logger.info({ sessionId }, 'Session no longer marked for cleanup (user reconnected)');
      }
    } else {
      const hashedId = hashString(clientId);
      session.users[clientId] = formatInternalUser({
        clientId: hashedId,
        name: user.name,
        avatarUrl: user.avatarUrl,
        isOnline: true,
        offlineAt: null,
        lastPing: now,
        ws,
      });
      this.logger.info({ clientId: ws.clientId, sessionId: ws.sessionId }, 'Client joined session');
    }

    this.setSession(ws.sessionId as string, session);
    this.sendMessage(
      ws,
      isNew
        ? formatSessionCreatedMsg({
            sessionId: ws.sessionId,
            clientId: ws.clientId,
          })
        : formatSessionJoinedMsg({
            sessionId: ws.sessionId,
            clientId: ws.clientId,
            session,
          })
    );
    this.broadcastToSession(
      session,
      formatUserConnectedMsg({
        sessionId: session.sessionId,
        user: session.users[clientId],
      }),
      clientId
    );
  }

  /**
   * Handles a session update request, updating session intervals and broadcasting changes.
   *
   * @param ws WebSocket connection for the client.
   * @param message The session update message.
   */
  private handleSessionUpdate(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'session_update') return;
    const { session: update } = message;

    const session = this.getClientSession(ws);
    if (!session) return;

    if (!update.intervals.items || !Array.isArray(update.intervals.items)) {
      return this.sendError(ws, 'Invalid intervals data');
    }

    session.name = update.name;
    session.description = update.description;
    session.intervals = update.intervals;
    session.timerInstance.updateIntervals(session.intervals.items);
    session.timer = session.timerInstance.getState();

    this.setSession(session.sessionId, session);
    this.broadcastToSession(session, formatIntervalsUpdatedMsg(session) as IntervalsUpdatedMessage, ws.clientId);
    this.broadcastTimerUpdate(session, ws.clientId);

    this.logger.info({ sessionId: ws.sessionId }, 'Intervals updated for session');
  }

  /**
   * Handles a timer update from the client, synchronizing timer state and broadcasting the update.
   *
   * @param ws WebSocket connection for the client.
   * @param message The timer update message containing the timer state.
   */
  private handleTimerUpdate(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'timer_update') return;
    const { timer } = message as TimerUpdateMessage;

    const session = this.getClientSession(ws);
    if (!session) return;

    session.timer = session.timerInstance.updateFromSource(timer);

    this.setSession(session.sessionId as string, session);
    this.broadcastTimerUpdate(session, ws.clientId);

    this.logger.debug(
      {
        clientId: ws.clientId,
        sessionId: ws.sessionId,
      },
      'Timer state updated from client'
    );
  }

  /**
   * Handles a user update request, updating user profile information and broadcasting the change.
   *
   * @param ws WebSocket connection for the client.
   * @param message The user update message containing updated user data.
   */
  private handleUserUpdate(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'user_update') return;
    const { user } = message as UserUpdateMessage;

    const clientId = ws.clientId as string;
    if (!clientId) return;

    const session = this.getClientSession(ws);
    if (!session || !session.users[clientId]) return;

    const existing = session.users[clientId];

    existing.name = user.name;
    existing.avatarUrl = user.avatarUrl;

    session.users[clientId] = existing;

    this.setSession(session.sessionId, session);
    this.broadcastToSession(
      session,
      formatUserUpdatedMsg({
        sessionId: session.sessionId,
        user: existing,
      } as UserUpdated),
      clientId
    );

    this.logger.info(
      {
        clientId,
        sessionId: ws.sessionId,
        userName: user.name,
      },
      'User updated profile in session'
    );
  }

  /**
   * Handles a request to get the list of connected users in the session.
   *
   * @param ws WebSocket connection for the client.
   * @param message The user list message.
   */
  private handleUserList(ws: ServerWebSocket, message: IncomingMessage): void {
    if (message.type !== 'user_list') return;

    const session = this.getClientSession(ws);
    if (!session) return;

    this.sendMessage(ws, formatConnectedUsersMsg(session));

    this.logger.debug(
      {
        clientId: ws.clientId,
        sessionId: ws.sessionId,
      },
      'Connected users list sent to client'
    );
  }

  /**
   * Creates a new session with the given session ID.
   *
   * @param sessionId The unique session ID.
   * @returns The newly created session object.
   */
  private createSession(sessionId: string): SessionInternal {
    const session = formatInternalSession({
      sessionId,
    });

    session.timerInstance.setState(session.timer);

    this.setSession(session.sessionId, session);

    return session;
  }

  /**
   * Sets the session object for the given session ID.
   *
   * @param sessionId The unique session ID.
   * @param session The session object to store.
   */
  private setSession(sessionId: string, session: SessionInternal): void {
    if (!sessionId) return;
    this.sessions.set(sessionId, formatInternalSession(session));
  }

  /**
   * Retrieves the session object for the given session ID.
   *
   * @param sessionId The unique session ID.
   * @returns The session object if found, otherwise null.
   */
  getSession(sessionId: string): SessionInternal | null {
    if (!sessionId) return null;
    return this.sessions.has(sessionId) ? this.sessions.get(sessionId)! : null;
  }

  /**
   * Retrieves the session associated with the given WebSocket client.
   *
   * @param ws WebSocket connection for the client.
   * @returns The session object if found, otherwise null.
   */
  private getClientSession(ws: ServerWebSocket): SessionInternal | null {
    if (ws.sessionId && this.sessions.has(ws.sessionId)) {
      const session = this.getSession(ws.sessionId);
      if (!session) return null;

      session.lastActivity = Date.now();
      this.setSession(session.sessionId, session);

      return session;
    }

    this.sendError(ws, 'Session not found');

    return null;
  }

  /**
   * Starts the periodic cleanup timer for inactive sessions and offline users.
   */
  private startCleanupTimer(): void {
    this.cleanup = setInterval(() => {
      this.cleanupInactiveSessions();
      this.cleanupOfflineUsers();
    }, parseInt(process.env.SESSION_CLEANUP_INTERVAL as string) || 300000);
  }

  /**
   * Cleans up sessions that have been inactive for a configured timeout period.
   */
  private cleanupInactiveSessions(): void {
    const now = Date.now();
    const timeout = 10 * 60 * 1000;
    let count = 0;

    this.sessions.forEach((session, sessionId) => {
      let cleanUp = false;

      const online = Object.values(session.users).filter((u) => u.ws && u.ws.readyState === 1).length;

      if (online === 0 && session.emptyAt && now - session.emptyAt > timeout) {
        cleanUp = true;
      }

      if (cleanUp) {
        this.sessions.delete(sessionId);
        count++;
      }
    });

    if (count > 0) this.logger.info({ count }, 'Cleaned up inactive sessions');
  }

  /**
   * Tracks users who have gone offline based on their WebSocket state.
   */
  private trackOfflineUsers(): void {
    const now = Date.now();

    this.sessions.forEach((session: SessionInternal) => {
      Object.values(session.users).forEach((user: UserInternal) => {
        const isOnline = user.ws && user.ws.readyState === 1;
        if (!isOnline && !user.offlineAt) {
          user.offlineAt = now;
        } else if (isOnline && user.offlineAt) {
          user.offlineAt = null;
        }
      });
    });
  }

  /**
   * Cleans up users who have been offline for longer than the configured timeout.
   */
  private cleanupOfflineUsers(): void {
    this.trackOfflineUsers();

    const now = Date.now();
    const timeout = parseInt(process.env.DISCONNECTED_USER_TIMEOUT as string) || 300000;
    let removed = 0;

    this.sessions.forEach((session: SessionInternal, sessionId: string) => {
      const remove: string[] = [];

      Object.entries(session.users).forEach(([clientId, user]) => {
        if (user.offlineAt && now - user.offlineAt > timeout) {
          remove.push(clientId);
        }
      });

      remove.forEach((clientId: string) => {
        const user = session.users[clientId];
        delete session.users[clientId];

        removed++;

        this.logger.info(
          {
            clientId,
            sessionId,
            userName: user.name,
          },
          'Removed offline user from session after timeout'
        );

        this.broadcastToSession(
          session,
          formatUserDisconnectedMsg({
            sessionId,
            user,
          })
        );
      });

      if (Object.keys(session.users).length === 0 && !session.emptyAt) {
        session.emptyAt = Date.now();
        this.logger.info({ sessionId }, 'Session marked for cleanup (all users removed after timeout)');
      }
    });

    if (removed > 0) this.logger.info({ removed }, 'Cleaned up offline users');
  }

  /**
   * Marks a client as offline in the session and schedules removal after timeout if not reconnected.
   *
   * @param sessionId The session ID from which to remove the client.
   * @param clientId The client ID to mark as offline.
   */
  removeClient(sessionId: string, clientId: string): void {
    const session = this.getSession(sessionId);
    if (!session) return;

    const user = session.users[clientId];
    if (user) {
      user.offlineAt = Date.now();
      user.ws = null;

      this.broadcastToSession(
        session,
        formatUserUpdatedMsg({
          sessionId,
          user,
        }),
        clientId
      );

      this.logger.info(
        {
          clientId,
          sessionId,
          userName: user.name,
        },
        'Client disconnected from session, will be removed after timeout if not reconnected'
      );

      const hasOnline = Object.values(session.users).some((u) => u.ws && u.ws.readyState === 1);
      if (!hasOnline && !session.emptyAt) {
        session.emptyAt = Date.now();
        this.logger.info({ sessionId }, 'Session marked for cleanup (all users offline)');
      }

      session.users[clientId] = user;
      this.setSession(sessionId, session);
    }
  }

  /**
   * Broadcasts the current timer state to all clients in the session, excluding the specified client if provided.
   *
   * @param session The session object whose timer state will be broadcast.
   * @param exclude Optional client ID to exclude from the broadcast.
   */
  private broadcastTimerUpdate(session: SessionInternal, exclude: string | null | undefined = null): void {
    session.timer = session.timerInstance.sync();
    this.broadcastToSession(session, formatTimerUpdatedMsg(session), exclude);
  }

  /**
   * Broadcasts a message to all clients in a session, excluding the specified client if provided.
   *
   * @param session The session object whose clients will receive the message.
   * @param message The message to broadcast.
   * @param exclude Optional client ID to exclude from the broadcast.
   */
  private broadcastToSession(
    session: SessionInternal,
    message: OutgoingMessage,
    exclude: string | null | undefined = null
  ): void {
    Object.entries(session.users).forEach(([clientId, user]) => {
      if (clientId !== exclude && user.ws && user.ws.readyState === 1) {
        this.sendMessage(user.ws, message);
      }
    });
  }

  /**
   * Sends a message to a specific WebSocket client.
   *
   * @param ws The WebSocket connection for the client.
   * @param message The message to send.
   */
  private sendMessage(ws: ServerWebSocket, message: OutgoingMessage | ErrorMessage): void {
    if (ws.readyState !== 1) return;

    try {
      ws.send(JSON.stringify(message));
    } catch (error) {
      this.logger.error({ error }, 'Error sending WebSocket message');
    }
  }

  /**
   * Sends an error message to a specific WebSocket client.
   *
   * @param ws The WebSocket connection for the client.
   * @param message The error message to send.
   */
  private sendError(ws: ServerWebSocket, message: string): void {
    const error = { message } as ErrorMessage;
    this.sendMessage(ws, formatErrorMsg(error));
  }

  /**
   * Disposes the session manager, clearing all sessions, handlers, and cleanup intervals.
   */
  dispose(): void {
    if (this.cleanup) {
      clearInterval(this.cleanup);
      this.cleanup = null;
    }
    this.sessions.clear();
    this.handlers.clear();
  }
}

export default SessionManager;
