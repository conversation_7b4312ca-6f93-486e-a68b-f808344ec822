/* Main CSS file with component imports */

/* Base styles and reset - must be first */
@import 'components/base.css';

/* Core component styles */
@import 'components/session.css';
@import 'components/timer.css';
@import 'components/controls.css';
@import 'components/intervals.css';

/* UI components */
@import 'components/buttons.css';
@import 'components/modal.css';
@import 'components/notifications.css';
@import 'components/status.css';

/* Feature-specific components */
@import 'components/share.css';
@import 'components/settings.css';
@import 'components/user.css';

/* Popup components */
@import 'components/popup.css';
@import 'components/users-popup.css';
@import 'components/sessions-popup.css';
@import 'components/confirm-dialog.css';

/* Responsive styles - must be last to ensure proper cascading */
@import 'components/responsive.css';
