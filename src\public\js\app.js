import Utils, { DOM, Events, Storage } from './utils.js';
import SocketClient from './socket.js';
import Coordinator from './coordinator.js';
import AlertSystem from './alerts.js';
import SessionManager from './sessions.js';
import IntervalsManager from './intervals.js';
import SettingsManager from './settings.js';
import ShareManager from './share.js';
import UserManager from './user.js';
import Timer from './timer.js';

/**
 * Timer application class
 */
class TimerApp {
  /**
   * Create a TimerApp instance and initialize the application state and components.
   */
  constructor() {
    this.isInitialized = false;
    this.isDisconnected = false;

    this.socket = null;
    this.coordinator = null;
    this.alerts = null;
    this.sessions = null;
    this.intervals = null;
    this.settings = null;
    this.share = null;
    this.user = null;
    this.timer = null;

    this.currentSessionId = null;
    this.currentSession = null;
    this.connectedUsers = null;
    this.currentUser = {
      hashedId: '',
      clientId: '',
      name: '',
      email: '',
      avatarUrl: '',
    };

    this.$statusEl = DOM.getId('connection-status');

    this._initialize();
  }

  /**
   * Start application initialization process.
   */
  _initialize() {
    if (this.isInitialized) return;

    console.log('Initializing Timer App...');

    if (document.readyState === 'loading') {
      console.log('Waiting for DOMContentLoaded to initialize app...');
      Events.on(document, 'DOMContentLoaded', this._initializeApp.bind(this));
    } else {
      console.log('DOMContentLoaded already fired, initializing app...');
      this._initializeApp();
    }
  }

  /**
   * Complete application setup and start the timer app.
   */
  _initializeApp() {
    try {
      this._components();
      this._listeners();
      this._shortcuts();
      this._visibility();
      this.connect();

      this.sessions.loadFromUrl();

      this.isInitialized = true;
      console.log('Timer App initialized successfully');
    } catch (error) {
      console.error('Failed to initialize app:', error);
    }
  }

  /**
   * Create instances of all application components.
   */
  _components() {
    this.socket = new SocketClient(this);
    this.coordinator = new Coordinator(this);
    this.alerts = new AlertSystem(this);
    this.sessions = new SessionManager(this);
    this.intervals = new IntervalsManager(this);
    this.settings = new SettingsManager(this);
    this.share = new ShareManager(this);
    this.user = new UserManager(this);
    this.timer = new Timer(this);
  }

  /**
   * Configure global event listeners for window events.
   */
  _listeners() {
    Events.on(window, 'beforeunload', this._beforeUnload.bind(this));
    Events.on(window, 'focus', this._windowFocus.bind(this));
    Events.on(this.$statusEl, 'click', () => {
      if (this.isDisconnected) this.connect();
    });
  }

  /**
   * Configure keyboard shortcuts for application controls.
   */
  _shortcuts() {
    Events.on(document, 'keydown', (event) => {
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
      }

      // Handle shortcuts
      switch (event.key) {
        case ' ': // Spacebar - Start/Pause timer
          event.preventDefault();
          this.timer?.toggleTimer();
          break;

        case 'ArrowRight': // Right arrow - Next interval
          event.preventDefault();
          this.timer?.next();
          break;

        case 's': // S - Show settings
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.settings?.show();
          }
          break;

        case 'i': // I - Show intervals
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.intervals?.show();
          }
          break;

        case 'u': // U - Show user profile
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.user?.show();
          }
          break;

        case 'p': // P - Show users list
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.sessions?.showUserList();
          }
          break;

        case 'l': // L - Show sessions list
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.sessions?.showSessionList();
          }
          break;

        case 'Escape': // Escape - Close modals
          DOM.hideAllModals();
          break;
      }
    });
  }

  /**
   * Setup page visibility event handling for timer and socket reconnect.
   */
  _visibility() {
    let eventName = 'visibilitychange';

    if (typeof document.hidden === 'undefined') {
      if (typeof document.webkitHidden !== 'undefined') {
        eventName = 'webkitvisibilitychange';
      } else if (typeof document.mozHidden !== 'undefined') {
        eventName = 'mozvisibilitychange';
      } else if (typeof document.msHidden !== 'undefined') {
        eventName = 'msvisibilitychange';
      }
    }

    Events.on(document, eventName, () => {
      if (document.hidden) return;
      this._pageVisible();
    });
  }

  /**
   * Update timer display when window regains focus.
   */
  _windowFocus() {
    if (!this.timer) return;
    this.timer.tickTimer();
  }

  /**
   * Reconnect WebSocket when page becomes visible.
   */
  _pageVisible() {
    if (!this.socket.isConnected()) {
      this.socket.connect();
    }
  }

  /**
   * Clean up resources before page unload.
   */
  _beforeUnload() {
    this.dispose();
  }

  /**
   * Clear the current session data.
   */
  clearCurrentSession() {
    this.currentSession = null;
    this.currentSessionId = null;
  }

  /**
   * Persist current session to local storage.
   */
  saveCurrentSession() {
    if (!this.currentSession) return;
    Storage.saveSession(this.currentSession);
  }

  /**
   * Update current session and initialize user data.
   * @param {Object} session - Session object to set as current
   * @returns {Promise<void>}
   */
  async setCurrentSession(session) {
    this.currentSession = session;
    this.currentSessionId = session.sessionId;
    await this.setCurrentUser(session.user);
  }

  /**
   * Get the current session data.
   * @returns {Object|null} Current session object or null
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Get the current session ID.
   * @returns {string|null} Current session ID or null
   */
  getCurrentSessionId() {
    if (!this.currentSessionId) {
      const session = this.getCurrentSession();
      this.currentSessionId = session ? session.sessionId : null;
    }
    return this.currentSessionId;
  }

  /**
   * Process and store user data with generated avatar.
   * @param {Object} user - User object containing user data
   * @returns {Promise<void>}
   */
  async setCurrentUser(user) {
    const clientId = Utils.isValidClientId(user?.clientId) ? user.clientId : Utils.generateClientId();
    const hashedId = await Utils.getSHA256(clientId);
    const current = {
      hashedId,
      clientId,
      name: String(user?.name ?? ''),
      email: String(user?.email ?? ''),
      avatarUrl: await Utils.getGravatarUrl(user?.avatarUrl || user?.email || hashedId),
    };
    this.currentUser = current;

    if (!this.currentSession) return;
    this.currentSession.user = current;
  }

  /**
   * Get the current user data.
   * @returns {Object} Current user object
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Reset current user to default empty state.
   */
  clearCurrentUser() {
    this.currentUser = {
      hashedId: '',
      clientId: '',
      name: '',
      email: '',
      avatarUrl: '',
    };
  }

  /**
   * Clear all connected users from the map.
   */
  clearConnectedUsers() {
    this.connectedUsers = null;
  }

  /**
   * Get the map of connected users.
   * @returns {Object|null} Map of connected users or null
   */
  getConnectedUsers() {
    return this.connectedUsers;
  }

  /**
   * Set the map of connected users.
   * @param {Object} users - Map of connected users
   */
  setConnectedUsers(users) {
    this.connectedUsers = users;
  }

  /**
   * Set a connected user in the map.
   * @param {Object} user - User object to add to connected users
   */
  setConnectedUser(user) {
    const connected = { ...user };
    if (!connected?.clientId) return;

    if (connected?.hashedId) {
      connected.clientId = connected.hashedId;
    }

    if (!this.connectedUsers) {
      this.connectedUsers = {};
    }

    this.connectedUsers[connected.clientId] = {
      clientId: connected.clientId,
      name: connected.name,
      avatarUrl: connected.avatarUrl,
      isOnline: connected.isOnline,
    };
  }

  /**
   * Delete a connected user from the map.
   * @param {Object|string} user - User object with clientId property or client ID string
   */
  deleteConnectedUser(user) {
    const clientId = typeof user === 'string' ? user : user.clientId;
    if (!this.connectedUsers || !clientId) return;
    delete this.connectedUsers[clientId];
    if (this.getConnectedUserCount() === 0) this.clearConnectedUsers();
  }

  /**
   * Get the number of connected users.
   * @returns {number} Number of connected users
   */
  getConnectedUserCount() {
    return this.connectedUsers ? Object.keys(this.connectedUsers).length : 0;
  }

  /**
   * Establish WebSocket connection to the server.
   */
  connect() {
    if (!this.socket) return;
    this.socket.connect();
  }

  /**
   * Update application state based on WebSocket events.
   * @param {string} type - The WebSocket event name
   * @param {Object} data - The WebSocket event data
   * @param {Object} session - Session object to update
   * @returns {Promise<void>}
   */
  async update(type, data, session) {
    switch (type) {
      case 'sessionCreated':
        await this.setCurrentSession(session);
        this.setCurrentUser(session.user);
        this.clearConnectedUsers();
        this.setConnectedUser(session.user);

        this.timer.reload();

        if (this.socket.isConnected()) {
          try {
            this.socket.updateIntervals(session);
            this.socket.updateTimer(session);
          } catch (error) {
            console.error('Failed to sync session data to server after sessionCreated:', error);
          }
        }
        break;

      case 'sessionJoined':
        await this.setCurrentSession(session);

        if (session.user.clientId !== data.clientId) {
          session.user.hashedId = await Utils.getSHA256(data.clientId);
          session.user.clientId = data.clientId;
          this.setCurrentUser(session.user);
          this.saveCurrentSession();
        }

        if (Object.keys(data.session.users).length === 0) {
          this.clearConnectedUsers();
          this.setConnectedUser(session.user);
        } else {
          this.setConnectedUsers(data.session.users);
        }

        this.timer.reload();

        if (session.timer.isRunning && !session.timer.isPaused) {
          this.timer.startTimer();
        }

        if (this.socket.isConnected()) {
          try {
            if (session.timer.isRunning && !data.session.timer.isRunning) {
              this.socket.updateTimer(session);
            }
          } catch (error) {
            console.warn('Failed to sync timer state to server after sessionJoined:', error);
          }
        }
        break;

      case 'intervalsUpdated':
        await this.setCurrentSession(session);
        if (this.timer.timerCore) {
          this.timer.timerCore.updateIntervals(session.intervals.items);
          session.timer = this.timer.timerCore.getState();
          this.saveCurrentSession();
        }
        break;

      case 'timerUpdated':
        await this.setCurrentSession(session);

        if (this.timer.timerCore) {
          this.timer.timerCore.updateFromSource(session.timer);
        }

        if (session.timer.isRunning && !session.timer.isPaused) {
          this.timer.startTimer();
        } else {
          this.timer.stopTimer();
        }
        break;

      case 'userConnected':
      case 'userUpdated':
        this.setConnectedUser(data.user);
        break;

      case 'userDisconnected':
        this.deleteConnectedUser(data.user);
        break;

      case 'connectedUsers':
        this.setConnectedUsers(data.users);
        break;

      case 'websocketConnected':
        this.isDisconnected = false;
        break;

      case 'websocketDisconnected':
        this.isDisconnected = true;
        break;
    }
  }

  /**
   * Restart the application by disconnecting, clearing state, and creating a new session.
   * @returns {Promise<void>}
   */
  async restart() {
    console.log('Restarting application...');
    this.socket.disconnect();

    this.clearCurrentSession();
    this.clearConnectedUsers();
    this.clearCurrentUser();

    this.socket.connect();

    await this.sessions.create();

    this.timer.reload();

    console.log('Application restarted');
    Events.dispatch(document, 'appRestarted');
  }

  /**
   * Dispose of all components that have a dispose method.
   */
  dispose() {
    Object.entries(this).forEach(([name, component]) => {
      if (component && typeof component.dispose === 'function') {
        try {
          console.log(`Disposing component: ${name}`);
          component.dispose();
        } catch (error) {
          console.warn(`Error disposing component ${name}:`, error);
        }
      }
    });
  }
}

window.app = new TimerApp();

export default TimerApp;
