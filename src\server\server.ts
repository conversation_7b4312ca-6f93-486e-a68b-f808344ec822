/**
 * Express + WebSocket server for collaborative timer (TypeScript)
 */

import type { ErrorMessage, Session } from '../types/messages';
import type { ServerWebSocket } from '../types/server';
import type { Logger } from 'pino';
import { createLogger } from './logger.js';
import { formatSession, formatPongMsg, formatErrorMsg } from './messages.js';
import SessionManager from './sessions.js';
import express, { Express, Request, Response, NextFunction } from 'express';
import http, { Server as HttpServer, IncomingMessage } from 'http';
import WebSocket, { WebSocketServer } from 'ws';
import path from 'path';
import dotenv from '@dotenvx/dotenvx';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config();

const environments = ['production', 'development'];
if (!environments.includes(process.env.NODE_ENV || '')) {
  process.env.NODE_ENV = 'production';
}

/**
 * Main server class for handling HTTP and WebSocket connections.
 */
class TimerServer {
  logger: Logger;
  express: Express;
  server: HttpServer;
  port: number;
  wss: WebSocketServer | null;
  wsPort: number | string;
  sessions: SessionManager;

  /**
   * Initialize the server, configure Express, and setup WebSocket.
   */
  constructor() {
    this.logger = createLogger('timer-server');
    this.express = express();
    this.server = http.createServer(this.express);
    this.port = Number(process.env.PORT) || 3000;
    this.wss = null;
    this.wsPort = Number(process.env.WS_PORT) || this.port;
    this.sessions = new SessionManager();

    this.setupExpress();
    this.setupWebSocket();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Setup Express middleware and static serving.
   *
   * Configures trust proxy, security headers, CORS (in development),
   * JSON and URL-encoded parsers, static file serving, and request logging.
   */
  setupExpress(): void {
    this.express.set('trust proxy', 1);

    this.express.use((_req: Request, res: Response, next: NextFunction) => {
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      next();
    });

    if (process.env.NODE_ENV === 'development') {
      this.express.use((req: Request, res: Response, next: NextFunction) => {
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        this.logger.debug({ method: req.method, url: req.url }, 'HTTP request');
        next();
      });
    }

    this.express.use(express.json({ limit: '10mb' }));
    this.express.use(express.urlencoded({ extended: true, limit: '10mb' }));

    this.express.use(
      express.static(path.join(__dirname, '../public'), {
        maxAge: '1h',
        etag: true,
      })
    );

    if (process.env.BUILD !== 'dist') {
      // Serve shared files in development mode for client access
      this.express.use(
        '/js/shared',
        express.static(path.join(__dirname, '../shared'), {
          maxAge: '1h',
          etag: true,
          setHeaders: (res, filePath) => {
            if (filePath.endsWith('.js')) {
              res.setHeader('Content-Type', 'application/javascript');
            }
          },
        })
      );
    }

    if (process.env.LOGGING !== 'false') {
      this.express.use((req: Request, _res: Response, next: NextFunction) => {
        this.logger.info({ method: req.method, url: req.url }, 'HTTP request');
        next();
      });
    }
  }

  /**
   * Setup WebSocket server.
   *
   * Initializes the WebSocket server, attaches connection and error handlers,
   * and sets up a periodic ping to clients.
   */
  setupWebSocket(): void {
    this.wss = new WebSocketServer({
      server: this.server,
      clientTracking: true,
      perMessageDeflate: false,
    });

    this.wss.on('connection', (ws: ServerWebSocket, req: IncomingMessage) => {
      this.handleWebSocketConnection(ws, req);
    });

    this.wss.on('error', (error: Error) => {
      this.logger.error({ error }, 'WebSocket server error');
    });

    setInterval(() => {
      if (!this.wss) return;

      this.wss.clients.forEach((ws: ServerWebSocket) => {
        if (ws.isAlive === false) {
          this.logger.debug('Terminating dead WebSocket connection');
          ws.terminate();
          return;
        }
        ws.isAlive = false;
        ws.ping();
      });
    }, 30000);
  }

  /**
   * Setup HTTP routes.
   *
   * Defines health check, session API, and main application routes.
   */
  setupRoutes(): void {
    this.express.get('/api/session/:sessionId', (req: Request, res: Response, next: NextFunction) => {
      const { sessionId } = req.params;
      const session = this.sessions.getSession(sessionId);
      if (!session) return next();
      res.json(formatSession(session) as Session);
    });

    this.express.get('/:sessionId?', (_req: Request, res: Response) => {
      res.sendFile(path.join(__dirname, '../public/index.html'));
    });
  }

  /**
   * Setup error handling.
   *
   * Registers Express error middleware and process-level error handlers for uncaught exceptions,
   * unhandled promise rejections, and termination signals.
   */
  setupErrorHandling(): void {
    this.express.use((error: Error, _req: Request, res: Response, next: NextFunction) => {
      this.logger.error({ error }, 'Express error');

      if (res.headersSent) {
        return next(error);
      }

      res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
      });
    });

    process.on('uncaughtException', (error: Error) => {
      this.logger.fatal({ error }, 'Uncaught Exception');
      this.stop('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
      this.logger.fatal({ reason, promise }, 'Unhandled Rejection');
      this.stop('UNHANDLED_REJECTION');
    });

    process.on('SIGTERM', () => {
      this.logger.info('SIGTERM received');
      this.stop('SIGTERM');
    });

    process.on('SIGINT', () => {
      this.logger.info('SIGINT received');
      this.stop('SIGINT');
    });
  }

  /**
   * Handle new WebSocket connection.
   *
   * @param ws WebSocket connection instance for the client.
   * @param req Incoming HTTP request that initiated the WebSocket connection.
   */
  handleWebSocketConnection(ws: ServerWebSocket, req: IncomingMessage): void {
    const clientIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    this.logger.info({ clientIp }, 'WebSocket connection established');

    ws.isAlive = true;
    ws.sessionId = null;
    ws.clientId = null;

    ws.on('pong', () => {
      ws.isAlive = true;
    });

    ws.on('message', (message: WebSocket.RawData) => {
      this.handleWebSocketMessage(ws, message);
    });

    ws.on('close', (code: number, reason: Buffer) => {
      this.handleWebSocketClose(ws, code, reason.toString());
    });

    ws.on('error', (error: Error) => {
      this.logger.error({ error, clientIp }, 'WebSocket error');
    });
  }

  /**
   * Handle WebSocket message from a client.
   *
   * @param ws WebSocket connection instance for the client.
   * @param message Raw message data received from the client.
   */
  handleWebSocketMessage(ws: ServerWebSocket, message: WebSocket.RawData): void {
    try {
      const data = JSON.parse(message.toString());

      if (data.type === 'ping') {
        ws.send(JSON.stringify(formatPongMsg()));
        return;
      }

      if (!data.type) {
        this.logger.warn('Message without type received');
        return;
      }

      this.sessions.handleMessage(ws, data);
    } catch (error) {
      this.logger.error({ error }, 'Error parsing WebSocket message');
      this.sendError(ws, 'Invalid message format');
    }
  }

  /**
   * Handle WebSocket connection close event.
   *
   * @param ws WebSocket connection instance for the client.
   * @param code WebSocket close code.
   * @param reason Reason for the connection closure.
   */
  handleWebSocketClose(ws: ServerWebSocket, code: number, reason: string): void {
    this.logger.info({ code, reason }, 'WebSocket connection closed');

    if (ws.sessionId && ws.clientId) {
      this.sessions.removeClient(ws.sessionId, ws.clientId);
    }
  }

  /**
   * Send error message to a WebSocket client.
   *
   * @param ws WebSocket connection instance for the client.
   * @param message Error message to send to the client.
   */
  sendError(ws: ServerWebSocket, message: string): void {
    if (ws.readyState === WebSocket.OPEN) {
      const error = { message } as ErrorMessage;
      ws.send(JSON.stringify(formatErrorMsg(error)));
    }
  }

  /**
   * Start the HTTP and WebSocket server.
   *
   * Begins listening for HTTP and WebSocket connections on the configured port.
   */
  start(): void {
    this.server.listen(this.port, () => {
      this.logger.info(
        {
          port: this.port,
          environment: process.env.NODE_ENV,
          websocketUrl: `ws://localhost:${this.port}`,
          webUrl: `http://localhost:${this.port}`,
        },
        'Timer server started'
      );
    });

    this.server.on('error', (error: NodeJS.ErrnoException) => {
      if (error.code === 'EADDRINUSE') {
        this.logger.fatal({ port: this.port }, 'Port already in use');
        process.exit(1);
      } else {
        this.logger.error({ error }, 'Server error');
      }
    });
  }

  /**
   * Graceful shutdown of the server and WebSocket connections.
   *
   * @param signal Name of the signal that triggered the shutdown (e.g., 'SIGTERM', 'SIGINT').
   */
  stop(signal: string): void {
    this.logger.info({ signal }, 'Graceful shutdown initiated');

    if (this.wss) {
      this.logger.debug('Closing WebSocket server...');
      this.wss.close(() => {
        this.logger.debug('WebSocket server closed');
      });
    }

    this.server.close(() => {
      this.logger.info('HTTP server closed');
      process.exit(0);
    });

    setTimeout(() => {
      this.logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  }
}

const server = new TimerServer();
server.start();

export default TimerServer;
