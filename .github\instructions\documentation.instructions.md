---
applyTo: '**'
---

# Documentation Guidelines

Guidelines for coding agents to follow when writing documentation.

## Documentation

- ALWAYS use clear, concise, and technical language when documenting.
- ALWAYS provide a header comment explaining the purpose of the file.
- ALWAYS use markdown format for documentation.
- ALWAYS use code blocks to show code examples.
- ALWAYS provide diagrams and images to explain complex concepts.
- ALWAYS provide links to external documentation and resources.
- ALWAYS use tables to present data clearly and logically.

### Plan Structure

- ALWAYS write the plan as a detailed implementation plan.
- ALWAYS provide enough context for coding agents to complete the implementation.
- ALWAYS begin the plan with a title.
- ALWAYS include an overview describing the task.
- ALWAYS provide a checklist of steps to complete the task.
- ALWAYS include a checklist of files to be created, modified, or removed.
- ALWAYS include a checklist of dependencies to be installed.
- ALWAYS include a checklist of tests to be created.
- ALWAYS include diagrams and images IF they help clarify steps or structures.
- ALWAYS include code snippets IF they illustrate specific implementation points.
- ALWAYS end with a summary of the plan.
- ALWAYS use well-organized headers and subheaders.
- DO feel free to deviate from this structure if the task requires a different - approach, but ALWAYS ensure clarity.
