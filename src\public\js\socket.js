import { DOM, Events } from './utils.js';

/**
 * WebSocket client manager
 */
class SocketClient {
  /**
   * Create a new WebSocketClient instance.
   * @param {Object} app - Main application instance
   */
  constructor(app) {
    this.app = app;

    this.ws = null;
    this.isConnecting = false;
    this.isConnection = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.handlers = new Map();
    this.heartbeatInterval = null;
    this.heartbeatTimeout = null;

    this.$avatarLarge = DOM.getId('user-avatar-large');
    this.$statusElement = DOM.getId('user-profile-btn');
    this.$statusText = DOM.getId('connection-status');

    this._initialize();
  }

  /**
   * Set up WebSocket client configuration and handlers.
   */
  _initialize() {
    this._messageListeners();
    this.renderStatus('disconnected');
  }

  /**
   * Configure message handlers for WebSocket event types.
   */
  _messageListeners() {
    this.handlers.set('session_created', this._sessionCreatedMsg.bind(this));
    this.handlers.set('session_joined', this._sessionJoinedMsg.bind(this));
    this.handlers.set('intervals_updated', this._intervalsUpdatedMsg.bind(this));
    this.handlers.set('timer_updated', this._timerUpdatedMsg.bind(this));
    this.handlers.set('user_connected', this._userConnectedMsg.bind(this));
    this.handlers.set('user_disconnected', this._userDisconnectedMsg.bind(this));
    this.handlers.set('user_updated', this._userUpdatedMsg.bind(this));
    this.handlers.set('connected_users', this._connectedUsersMsg.bind(this));
  }

  /**
   * Set up event handlers for the WebSocket instance.
   */
  _socketListeners() {
    this.ws.onopen = () => {
      this.isConnection = true;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.renderStatus('connected');
      this._startHeartbeat();

      console.log('WebSocket connected');
      Events.dispatch(document, 'websocketConnected');
    };

    this.ws.onclose = (event) => {
      this._disconnected(event);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this._connectionError();
    };

    this.ws.onmessage = (event) => {
      this._processMessage(event);
    };
  }

  /**
   * Handle WebSocket disconnection event.
   * @param {CloseEvent} event - The close event from the WebSocket
   */
  _disconnected(event) {
    this.isConnection = false;
    this.isConnecting = false;
    this._stopHeartbeat();

    console.log('WebSocket disconnected:', event.code, event.reason);
    Events.dispatch(document, 'websocketDisconnected');

    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this._scheduleReconnect();
    } else {
      this.renderStatus('disconnected');
    }
  }

  /**
   * Process connection errors and attempt reconnection.
   */
  _connectionError() {
    this.isConnecting = false;
    this.renderStatus('error');

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this._scheduleReconnect();
    }
  }

  /**
   * Queue reconnection attempt with increasing delay.
   */
  _scheduleReconnect() {
    this.reconnectAttempts++;
    this.renderStatus('reconnecting');

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      if (!this.isConnection) {
        console.log(`Reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        this.connect();
      }
    }, delay);
  }

  /**
   * Begin periodic ping messages to maintain connection.
   */
  _startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnection) {
        this.sendMessage({
          type: 'ping',
        });

        this.heartbeatTimeout = setTimeout(() => {
          console.warn('Heartbeat timeout, closing connection');
          this.ws.close();
        }, 5000);
      }
    }, 30000);
  }

  /**
   * Stop the heartbeat interval and timeout.
   */
  _stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }

  /**
   * Handle an incoming WebSocket message event.
   * @param {MessageEvent} event - The message event containing data from the server
   */
  _processMessage(event) {
    try {
      const message = JSON.parse(event.data);

      if (message.type === 'pong') {
        if (this.heartbeatTimeout) {
          clearTimeout(this.heartbeatTimeout);
          this.heartbeatTimeout = null;
        }
        return;
      }

      const handler = this.handlers.get(message.type);
      if (handler) {
        handler(message);
      } else {
        console.warn('No handler for message type:', message.type);
      }

      Events.dispatch(document, 'websocketMessage', { message });
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Handle a session created message from the server.
   * @param {Object} message - The message data for session creation
   */
  _sessionCreatedMsg(message) {
    Events.dispatch(document, 'sessionCreated', message);
  }

  /**
   * Handle a session joined message from the server.
   * @param {Object} message - The message data for session join
   */
  _sessionJoinedMsg(message) {
    Events.dispatch(document, 'sessionJoined', message);
  }

  /**
   * Handle a intervals updated message from the server.
   * @param {Object} message - The message data for updated intervals
   */
  _intervalsUpdatedMsg(message) {
    Events.dispatch(document, 'intervalsUpdated', message);
  }

  /**
   * Handle a timer updated message from the server.
   * @param {Object} message - The message data for updated timer
   */
  _timerUpdatedMsg(message) {
    Events.dispatch(document, 'timerUpdated', message);
  }

  /**
   * Handle a user connected message from the server.
   * @param {Object} message - The message data for user connection
   */
  _userConnectedMsg(message) {
    Events.dispatch(document, 'userConnected', message);
  }

  /**
   * Handle a user disconnected message from the server.
   * @param {Object} message - The message data for user disconnection
   */
  _userDisconnectedMsg(message) {
    Events.dispatch(document, 'userDisconnected', message);
  }

  /**
   * Handle a user updated message from the server.
   * @param {Object} message - The message data for user update
   */
  _userUpdatedMsg(message) {
    Events.dispatch(document, 'userUpdated', message);
  }

  /**
   * Handle a connected users message from the server.
   * @param {Object} message - The message data for connected users
   */
  _connectedUsersMsg(message) {
    Events.dispatch(document, 'connectedUsers', message);
  }

  /**
   * Establish WebSocket connection to server.
   */
  connect() {
    if (this.isConnection || this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    this.renderStatus('connecting');

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    const port = window.location.port || (protocol === 'wss:' ? 443 : 80);
    const wsUrl = `${protocol}//${host}:${port === '80' || port === '443' ? '' : port}`;

    try {
      this.ws = new WebSocket(wsUrl);
      this._socketListeners();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this._connectionError();
    }
  }

  /**
   * Close WebSocket connection and clean up resources.
   */
  disconnect() {
    this._stopHeartbeat();

    if (this.ws) {
      this.ws.close(1000, 'Client disconnecting');
      this.ws = null;
    }

    this.isConnection = false;
    this.isConnecting = false;
    this.renderStatus('disconnected');
  }

  /**
   * Get the current WebSocket connection state.
   * @returns {boolean} True if the WebSocket is connected, otherwise false
   */
  isConnected() {
    return this.isConnection;
  }

  /**
   * Send a message to the WebSocket server.
   * @param {Object} message - The message object to send
   */
  sendMessage(message) {
    if (this.isConnection && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
      }
    }
  }

  /**
   * Request to join session with user data.
   * @param {string} sessionId - The session ID to join or create
   * @param {Object|null} [user=null] - The user data to send (optional)
   */
  joinSession(sessionId, user = null) {
    this.sendMessage({
      type: 'join_session',
      sessionId,
      user: {
        clientId: user?.clientId || '',
        name: user?.name || '',
        avatarUrl: user?.avatarUrl || '',
      },
    });
  }

  /**
   * Request timer start for current session.
   */
  startTimer() {
    this.sendMessage({
      type: 'timer_start',
    });
  }

  /**
   * Request timer pause for current session.
   */
  pauseTimer() {
    this.sendMessage({
      type: 'timer_pause',
    });
  }

  /**
   * Request timer stop for current session.
   */
  stopTimer() {
    this.sendMessage({
      type: 'timer_stop',
    });
  }

  /**
   * Set timer repeat mode for current session.
   * @param {boolean} repeat - The repeat mode state to set
   */
  timerRepeat(repeat) {
    this.sendMessage({
      type: 'timer_repeat',
      repeat: Boolean(repeat ?? false),
    });
  }

  /**
   * Move to the next interval in the timer.
   */
  nextInterval() {
    this.sendMessage({
      type: 'timer_next_interval',
    });
  }

  /**
   * Send session intervals update to server.
   * @param {Object} session - The session object whose intervals are being updated
   */
  updateIntervals(session) {
    this.sendMessage({
      type: 'intervals_update',
      session: {
        name: session?.name,
        description: session?.description,
        intervals: session?.intervals || { lastUpdated: Date.now(), items: [] },
      },
    });
  }

  /**
   * Send an updated timer state for a session to the server.
   * @param {Object} session - The session object whose timer is being updated
   */
  updateTimer(session) {
    const timer = session?.timer;

    this.sendMessage({
      type: 'timer_update',
      timer: {
        repeat: timer?.repeat,
        currentInterval: timer?.currentInterval,
        timeRemaining: timer?.timeRemaining,
        isRunning: timer?.isRunning,
        isPaused: timer?.isPaused,
      },
    });
  }

  /**
   * Update the user profile for a session.
   * @param {Object} session - The session object containing user data
   */
  updateUser(session) {
    const user = session?.user;

    this.sendMessage({
      type: 'user_update',
      user: {
        clientId: user?.clientId,
        name: user?.name,
        avatarUrl: user?.avatarUrl,
      },
    });
  }

  /**
   * Request the list of connected users from the server.
   */
  getConnectedUsers() {
    this.sendMessage({
      type: 'get_connected_users',
    });
  }

  /**
   * Update the connection status in the UI.
   * @param {string} status - The current connection status
   */
  renderStatus(status) {
    const classes = ['connected', 'connecting', 'reconnecting', 'error', 'disconnected'];

    this.$avatarLarge.classList.remove(...classes);
    this.$statusElement.classList.remove(...classes);
    this.$statusText.classList.remove(...classes);

    switch (status) {
      case 'connected':
        this.$avatarLarge.classList.add('connected');
        this.$statusElement.classList.add('connected');
        this.$statusText.classList.add('connected');
        this.$statusText.textContent = 'Connected';
        this.$statusText.style.display = 'none';
        break;

      case 'connecting':
        this.$avatarLarge.classList.add('connecting');
        this.$statusElement.classList.add('connecting');
        this.$statusText.classList.add('connecting');
        this.$statusText.textContent = 'Connecting...';
        this.$statusText.style.display = 'inline';
        break;

      case 'reconnecting':
        this.$avatarLarge.classList.add('connecting');
        this.$statusElement.classList.add('connecting');
        this.$statusText.classList.add('connecting');
        this.$statusText.textContent = `Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`;
        this.$statusText.style.display = 'inline';
        break;

      case 'error':
        this.$avatarLarge.classList.add('error');
        this.$statusElement.classList.add('error');
        this.$statusText.classList.add('error');
        this.$statusText.textContent = 'Connection failed';
        this.$statusText.style.display = 'inline';
        break;

      default:
        this.$avatarLarge.classList.add('disconnected');
        this.$statusElement.classList.add('disconnected');
        this.$statusText.classList.add('disconnected');
        this.$statusText.textContent = 'Disconnected';
        this.$statusText.style.display = 'inline';
        break;
    }
  }
}

export default SocketClient;
