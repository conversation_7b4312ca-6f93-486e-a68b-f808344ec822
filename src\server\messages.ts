/**
 * Handle websocket messages, formatting and data sanitization
 */

import type {
  ConnectedUsersMessage,
  ErrorMessage,
  GetConnectedUsersMessage,
  IncomingMessage,
  JoinSessionMessage,
  PingMessage,
  PongMessage,
  Interval,
  Intervals,
  SessionUpdatedMessage,
  SessionUpdateMessage,
  Session,
  SessionCreated,
  SessionCreatedMessage,
  SessionInternal,
  SessionJoined,
  SessionJoinedMessage,
  SessionNew,
  TimerState,
  TimerStateInternal,
  TimerUpdatedMessage,
  TimerUpdateMessage,
  UnknownIncomingMessage,
  User,
  UserConnectedMessage,
  UserDisconnectedMessage,
  UserInternal,
  UsersList,
  UsersListInternal,
  UserUpdated,
  UserUpdatedMessage,
  UserUpdateMessage,
} from '../types/messages';
import {
  SESSION_ID_REGEX,
  CLIENT_ID_REGEX,
  MAX_STRING_LENGTH,
  MAX_NAME_LENGTH,
  MAX_URL_LENGTH,
  MAX_DURATION,
  MIN_DURATION,
  DEFAULT_DURATION,
} from '../shared/constants.js';
import TimerCore from '../shared/timer-core.js';
import crypto from 'crypto';

/**
 * Generate a UUID v4 string.
 *
 * @returns a randomly generated UUID v4 string.
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Hash a string using SHA256.
 *
 * @param input the string to hash.
 * @returns the SHA256 hash of the input string as a hexadecimal string.
 */
export function hashString(input: string): string {
  return crypto
    .createHash('sha256')
    .update(input ?? '')
    .digest('hex');
}

// =============================================================================
// BASE FORMATTERS
// =============================================================================

/**
 * Format a session ID string. Returns the trimmed session ID if valid, otherwise an empty string.
 *
 * @param input the session ID string to format.
 * @returns the formatted session ID if valid, otherwise an empty string.
 */
export function formatSessionId(input: string): string {
  const sessionId = (input || '').trim();
  return SESSION_ID_REGEX.test(sessionId) ? sessionId : '';
}

/**
 * Format a client ID string. Returns the lowercased, trimmed client ID if valid, otherwise generates a new UUID.
 *
 * @param input the client ID string to format.
 * @returns the formatted client ID if valid, otherwise a new UUID.
 */
export function formatClientId(input: string): string {
  const clientId = (input || '').toLowerCase().trim();
  return CLIENT_ID_REGEX.test(clientId) ? clientId : generateUUID();
}

/**
 * Format a user object for external use.
 *
 * @param user the user object to format (User or UserInternal).
 * @returns a formatted User object with trimmed and validated fields.
 */
export function formatUser(user: User | UserInternal): User {
  return {
    clientId: user?.clientId || '',
    name: (user?.name || '').trim().substring(0, MAX_NAME_LENGTH),
    avatarUrl: (user?.avatarUrl || '').trim().substring(0, MAX_URL_LENGTH),
    isOnline: 'offlineAt' in user ? Boolean(!user.offlineAt) : true, // offlineAt is only on UserInternal
  };
}

/**
 * Format a users list for external use.
 *
 * Users are keyed by hashedId, not clientId.
 *
 * @param users the users list to format (UsersList or UsersListInternal).
 * @returns a formatted UsersList keyed by hashedId.
 */
export function formatUsers(users: UsersList | UsersListInternal): UsersList {
  return Object.values(users || {}).reduce((acc, user) => {
    if (user?.clientId) {
      acc[user.clientId] = formatUser(user);
    }
    return acc;
  }, {} as UsersList);
}

/**
 * Format a interval object for external use.
 *
 * @param interval the interval object to format.
 * @returns a formatted Interval object with validated and sanitized fields.
 */
export function formatInterval(interval: Interval): Interval {
  return {
    name: (interval?.name || '').trim().substring(0, MAX_NAME_LENGTH),
    duration: Math.max(MIN_DURATION, Math.min(MAX_DURATION, Number(interval?.duration || DEFAULT_DURATION))),
    alert: (interval?.alert || '').trim().substring(0, MAX_NAME_LENGTH) || 'Default',
    customCSS: (interval?.customCSS || '').trim(), // TODO: Sanitize CSS
  };
}

/**
 * Format a intervals collection for external use.
 *
 * @param intervals the intervals object to format.
 * @returns a formatted Intervals object with validated items.
 */
export function formatIntervals(intervals: Intervals): Intervals {
  return {
    lastUpdated: intervals?.lastUpdated || Date.now(),
    items: Array.isArray(intervals?.items) ? intervals.items.map(formatInterval) : [],
  };
}

/**
 * Format a timer state object for external use.
 *
 * @param timer the timer state object to format.
 * @returns a shallow copy of the timer state object.
 */
export function formatTimer(timer: TimerState | TimerStateInternal): TimerState {
  return {
    repeat: Boolean(timer?.repeat ?? false),
    currentInterval: Math.max(0, Math.min(timer?.currentInterval || 0, Number.MAX_SAFE_INTEGER)),
    timeRemaining: Math.max(0, Math.min(timer?.timeRemaining || DEFAULT_DURATION * 1000, MAX_DURATION * 1000)),
    isRunning: Boolean(timer?.isRunning ?? false),
    isPaused: Boolean(timer?.isPaused ?? false),
  };
}

/**
 * Format a session object for external use.
 *
 * @param session the session object to format (Session or SessionInternal).
 * @returns a formatted Session object with validated fields and formatted users.
 */
export function formatSession(session: Session | SessionInternal): Session {
  return {
    sessionId: formatSessionId(session?.sessionId || ''),
    name: (session?.name || '').trim().substring(0, MAX_STRING_LENGTH),
    description: (session?.description || '').trim().substring(0, MAX_STRING_LENGTH),
    intervals: formatIntervals(session?.intervals || {}),
    timer: formatTimer(session?.timer || {}),
    users: formatUsers(session?.users || {}),
  };
}

// =============================================================================
// INCOMING MESSAGE FORMATTERS
// =============================================================================

/**
 * Format a session join message for processing.
 *
 * @param message the session join message to format.
 * @returns a formatted JoinSessionMessage with validated sessionId and user.
 */
export function formatSessionJoinMsg(message: JoinSessionMessage): JoinSessionMessage {
  return {
    type: 'session_join',
    sessionId: formatSessionId(message?.sessionId || ''),
    user: formatUser(message?.user || {}),
  };
}

/**
 * Format a session update message for processing.
 *
 * @param message the session update message to format.
 * @returns a formatted SessionUpdateMessage with validated session fields.
 */
export function formatSessionUpdateMsg(message: SessionUpdateMessage): SessionUpdateMessage {
  const session = message?.session;
  return {
    type: 'session_update',
    session: {
      name: (session?.name || '').trim().substring(0, MAX_STRING_LENGTH),
      description: (session?.description || '').trim().substring(0, MAX_STRING_LENGTH),
      intervals: formatIntervals(session?.intervals || {}),
    },
  };
}

/**
 * Format a timer update message for processing.
 *
 * @param message the timer update message to format.
 * @returns a formatted TimerUpdateMessage with formatted timer.
 */
export function formatTimerUpdateMsg(message: TimerUpdateMessage): TimerUpdateMessage {
  return {
    type: 'timer_update',
    timer: formatTimer(message?.timer || {}),
  };
}

/**
 * Format a user update message for processing.
 *
 * @param message the user update message to format.
 * @returns a formatted UserUpdateMessage with formatted user.
 */
export function formatUserUpdateMsg(message: UserUpdateMessage): UserUpdateMessage {
  return {
    type: 'user_update',
    user: formatUser(message?.user || {}),
  };
}

/**
 * Create a user list message.
 *
 * @returns a GetConnectedUsersMessage object.
 */
export function formatUserListMsg(): GetConnectedUsersMessage {
  return { type: 'user_list' };
}

/**
 * Create a ping message.
 *
 * @returns a PingMessage object.
 */
export function formatPingMsg(): PingMessage {
  return { type: 'ping' };
}

// =============================================================================
// OUTGOING MESSAGE FORMATTERS
// =============================================================================

/**
 * Format a session created message for outgoing communication.
 *
 * @param message the session created object to format.
 * @returns a formatted SessionCreatedMessage with validated sessionId and clientId.
 */
export function formatSessionCreatedMsg(message: SessionCreated): SessionCreatedMessage {
  return {
    type: 'session_created',
    sessionId: formatSessionId(message?.sessionId || ''),
    clientId: formatClientId(message?.clientId || ''),
  };
}

/**
 * Format a session joined message for outgoing communication.
 *
 * @param message the session joined object to format.
 * @returns a formatted SessionJoinedMessage with validated sessionId, clientId, and session.
 */
export function formatSessionJoinedMsg(message: SessionJoined): SessionJoinedMessage {
  return {
    type: 'session_joined',
    sessionId: formatSessionId(message?.sessionId || ''),
    clientId: formatClientId(message?.clientId || ''),
    session: formatSession(message?.session || {}),
  };
}

/**
 * Format a user connected message for outgoing communication.
 *
 * @param message the user updated object to format.
 * @returns a formatted UserConnectedMessage with validated sessionId and user.
 */
export function formatUserConnectedMsg(message: UserUpdated): UserConnectedMessage {
  return {
    type: 'user_connected',
    sessionId: formatSessionId(message?.sessionId || ''),
    user: formatUser(message?.user || {}),
  };
}

/**
 * Format a user disconnected message for outgoing communication.
 *
 * @param message the user updated object to format.
 * @returns a formatted UserDisconnectedMessage with validated sessionId and user.
 */
export function formatUserDisconnectedMsg(message: UserUpdated): UserDisconnectedMessage {
  return {
    type: 'user_disconnected',
    sessionId: formatSessionId(message?.sessionId || ''),
    user: formatUser(message?.user || {}),
  };
}

/**
 * Format a user updated message for outgoing communication.
 *
 * @param message the user updated object to format.
 * @returns a formatted UserUpdatedMessage with validated sessionId and user.
 */
export function formatUserUpdatedMsg(message: UserUpdated): UserUpdatedMessage {
  return {
    type: 'user_updated',
    sessionId: formatSessionId(message.sessionId || ''),
    user: formatUser(message.user || {}),
  };
}

/**
 * Format a connected users message for outgoing communication.
 *
 * @param session the session object to format.
 * @returns a formatted ConnectedUsersMessage with validated sessionId and users.
 */
export function formatConnectedUsersMsg(session: SessionInternal): ConnectedUsersMessage {
  return {
    type: 'connected_users',
    sessionId: formatSessionId(session.sessionId || ''),
    users: formatUsers(session?.users || {}),
  };
}

/**
 * Format a session updated message for outgoing communication.
 *
 * @param session the session object to format.
 * @returns a formatted SessionUpdatedMessage with validated sessionId and session fields.
 */
export function formatSessionUpdatedMsg(session: SessionInternal): SessionUpdatedMessage {
  return {
    type: 'session_updated',
    sessionId: formatSessionId(session?.sessionId || ''),
    session: {
      name: (session?.name || '').trim(),
      description: (session?.description || '').trim(),
      intervals: formatIntervals(session?.intervals || {}),
    },
  };
}

/**
 * Format a timer updated message for outgoing communication.
 *
 * @param session the session object to format.
 * @returns a formatted TimerUpdatedMessage with validated sessionId and timer.
 */
export function formatTimerUpdatedMsg(session: SessionInternal): TimerUpdatedMessage {
  return {
    type: 'timer_updated',
    sessionId: formatSessionId(session?.sessionId || ''),
    timer: formatTimer(session?.timer || {}),
  };
}

/**
 * Create a pong message.
 *
 * @returns a PongMessage object.
 */
export function formatPongMsg(): PongMessage {
  return { type: 'pong' };
}

/**
 * Format an error message for outgoing communication.
 *
 * @param error the error message object to format.
 * @returns a formatted ErrorMessage with trimmed message.
 */
export function formatErrorMsg(error: ErrorMessage): ErrorMessage {
  return {
    type: 'error',
    message: (typeof error.message === 'string' ? error.message.trim() : error?.message) || 'Unknown error',
  };
}

// =============================================================================
// INTERNAL STRUCTURE FORMATTERS
// =============================================================================

/**
 * Format a user object for internal use, adding connection state fields.
 *
 * @param input the user object to format as internal (User or UserInternal).
 * @returns a formatted UserInternal object with connection state fields.
 */
export function formatInternalUser(input: User | UserInternal): UserInternal {
  const user = input as UserInternal;
  return {
    ...formatUser(user),

    // Internal connection state, these properties do not exist in User type
    lastPing: user?.lastPing || Date.now(),
    offlineAt: user?.offlineAt || 0,
    ws: user?.ws || null,
  };
}

/**
 * Format a users list for internal use, adding connection state fields to each user.
 *
 * Internal users are keyed by clientId, not hashedId.
 *
 * @param users the users list to format as internal (UsersList or UsersListInternal).
 * @returns a formatted UsersListInternal object with connection state fields for each user.
 */
export function formatInternalUsers(input: UsersList | UsersListInternal): UsersList {
  const users = input as UsersListInternal;
  return Object.entries(users || {}).reduce((acc, [key, user]) => {
    acc[key] = formatInternalUser(user);
    return acc;
  }, {} as UsersListInternal);
}

/**
 * Format a timer state object for internal use, adding timing state fields.
 *
 * @param input the timer state object to format as internal (TimerState or TimerStateInternal).
 * @returns a formatted TimerStateInternal object with timing state fields.
 */
export function formatInternalTimer(input: TimerState | TimerStateInternal): TimerStateInternal {
  const timer = input as TimerStateInternal;
  return {
    ...formatTimer(timer),

    // Internal timing state, these properties do not exist in TimerState type
    startedAt: timer?.startedAt || 0,
    startedInterval: timer?.startedInterval || 0,
    pausedAt: timer?.pausedAt || 0,
    timePaused: timer?.timePaused || 0,
  };
}

/**
 * Format a session object for internal use, adding timer instance and activity fields.
 *
 * @param input the session object to format as internal (Session, SessionInternal, or SessionNew).
 * @returns a formatted SessionInternal object with timer instance and activity fields.
 */
export function formatInternalSession(input: Session | SessionInternal | SessionNew): SessionInternal {
  const session = input as SessionInternal;
  return {
    sessionId: formatSessionId(session?.sessionId || ''),
    name: (session?.name || '').trim().substring(0, MAX_STRING_LENGTH),
    description: (session?.description || '').trim().substring(0, MAX_STRING_LENGTH),
    intervals: formatIntervals(session?.intervals || {}),
    timer: formatInternalTimer(session?.timer || {}),
    timerInstance: session?.timerInstance || new TimerCore(session?.intervals?.items || []),
    users: formatInternalUsers(session?.users || {}),
    createdAt: session?.createdAt || Date.now(),
    lastActivity: session?.lastActivity || Date.now(),
    emptyAt: session?.emptyAt || 0,
  };
}

// =============================================================================
// MAIN FORMATTER DISPATCHER
// =============================================================================

/**
 * Format an incoming message with strict explicit typing. Dispatches to the appropriate formatter based on message type.
 *
 * @param data the incoming message object to format.
 * @returns the formatted IncomingMessage.
 * @throws Error if the message type is unknown.
 */
export function formatIncoming(
  data:
    | JoinSessionMessage
    | SessionUpdateMessage
    | TimerUpdateMessage
    | UserUpdateMessage
    | GetConnectedUsersMessage
    | PingMessage
    | UnknownIncomingMessage
): IncomingMessage {
  switch (data.type) {
    case 'session_join':
      return formatSessionJoinMsg(data);
    case 'session_update':
      return formatSessionUpdateMsg(data);
    case 'timer_update':
      return formatTimerUpdateMsg(data);
    case 'user_update':
      return formatUserUpdateMsg(data);
    case 'user_list':
      return formatUserListMsg();
    case 'ping':
      return formatPingMsg();
    default:
      throw new Error('Invalid message type');
  }
}
