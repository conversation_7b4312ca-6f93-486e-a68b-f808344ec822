# Refactor WebSocket Endpoints

Simplify WebSocket message handling by consolidating timer control messages into a single `timer_update` message.

## Implementation Details

### messages.ts

Refactor WSS Endpoints by removing unnecessary timer messages, and renaming others for consistency:

Here is the original message handling code:

```javascript
  switch (data.type) {
    case 'join_session':
      return formatJoinMsg(data);
    case 'intervals_update':
      return formatSegUpdateMsg(data);
    case 'timer_start':
      return formatStartMsg();
    case 'timer_pause':
      return formatPauseMsg();
    case 'timer_stop':
      return formatStopMsg();
    case 'timer_repeat':
      return formatRepeatMsg(data);
    case 'timer_next_interval':
      return formatNextMsg();
    case 'timer_update':
      return formatUpdateMsg(data);
    case 'user_update':
      return formatUserUpdateMsg(data);
    case 'get_connected_users':
      return formatGetUsersMsg();
    case 'ping':
      return formatPingMsg();
    default:
      throw new Error('Invalid message type');
```

The above code block should be refactored as follows:

```javascript
  switch (data.type) {
    case 'session_join':
      return formatSessionJoinMsg(data); // rename from formatJoinMsg
    case 'session_update': // rename from intervals_update
      return formatSessionUpdateMsg(data); // rename from formatSegUpdateMsg
    case 'timer_update': // remove redundant timer messages and leave only `timer_update`
      return formatTimerUpdateMsg(data); // rename from formatUpdateMsg
    case 'user_update':
      return formatUserUpdateMsg(data);
    case 'user_list': // rename from get_connected_users
      return formatUserListMsg(); // rename from formatGetUsersMsg
    case 'ping':
      return formatPingMsg();
    default:
      throw new Error('Invalid message type');
```

Ensure that format methods are renamed accordingly.

### sessions.ts

Refactor session handlers to remove obsolete handlers and rename others for consistency.

Here is the original setup:

```javascript
  private setupHandlers(): void {
    this.handlers.set('join_session', this.handleJoinSession.bind(this));
    this.handlers.set('intervals_update', this.handleIntervalsUpdate.bind(this));
    this.handlers.set('timer_start', this.handleTimerStart.bind(this));
    this.handlers.set('timer_pause', this.handleTimerPause.bind(this));
    this.handlers.set('timer_stop', this.handleTimerStop.bind(this));
    this.handlers.set('timer_repeat', this.handleTimerRepeat.bind(this));
    this.handlers.set('timer_next_interval', this.handleTimerNextInterval.bind(this));
    this.handlers.set('timer_update', this.handleTimerUpdate.bind(this));
    this.handlers.set('user_update', this.handleUserUpdate.bind(this));
    this.handlers.set('get_connected_users', this.handleGetConnectedUsers.bind(this));
  }
```

The above code block should be refactored as follows:

```javascript
  private setupHandlers(): void {
    this.handlers.set('session_join', this.handleSessionJoin.bind(this));
    this.handlers.set('session_update', this.handleSessionUpdate.bind(this));
    this.handlers.set('timer_update', this.handleTimerUpdate.bind(this));
    this.handlers.set('user_update', this.handleUserUpdate.bind(this));
    this.handlers.set('user_list', this.handleUserListUsers.bind(this));
  }
```

Ensure that handler methods are renamed accordingly.
