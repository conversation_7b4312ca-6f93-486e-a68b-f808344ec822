{"name": "fycus", "version": "1.0.0", "description": "Real-time collaborative focus and break timer.", "type": "module", "main": "dist/server/server.js", "scripts": {"start": "cross-env BUILD=dist node dist/server/server.js", "dev": "nodemon --exec tsx src/server/server.ts", "test": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js --watch", "test:coverage": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js --coverage", "lint": "eslint", "lint:fix": "eslint --fix", "build": "tsx scripts/build.ts"}, "dependencies": {"@dotenvx/dotenvx": "^1.48.2", "cross-env": "^7.0.3", "express": "^4.18.2", "pino": "^9.9.0", "ws": "^8.14.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@playwright/test": "^1.53.1", "@types/express": "^4.17.23", "@types/jest": "^30.0.0", "@types/node": "^20.19.1", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "esbuild": "0.25.5", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "jiti": "^2.4.2", "nodemon": "^3.0.1", "pino-pretty": "^13.1.1", "playwright": "^1.53.1", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "keywords": ["pomodoro", "timer", "focus", "websocket"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "GPLv3", "engines": {"node": ">=16.0.0"}}