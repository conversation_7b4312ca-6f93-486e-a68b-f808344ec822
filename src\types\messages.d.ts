import type { ServerWebSocket } from './server';
import type TimerCore from '../shared/timer-core';

export interface User {
  clientId: string;
  name: string;
  avatarUrl: string;
  isOnline: boolean;
}

export interface UserInternal extends User {
  lastPing?: number;
  offlineAt?: number | null;
  ws?: ServerWebSocket | null;
}

export interface UserUpdated {
  sessionId: string;
  user: UserInternal;
}

export interface UsersList {
  [key: string]: User;
}

export interface UsersListInternal {
  [key: string]: UserInternal;
}

export interface TimerState {
  repeat: boolean;
  currentInterval: number;
  timeRemaining: number;
  isRunning: boolean;
  isPaused: boolean;
}

export interface TimerStateInternal extends TimerState {
  startedAt?: number;
  startedInterval?: number;
  pausedAt?: number;
  timePaused?: number;
}

export interface Interval {
  name: string;
  duration: number;
  alert: string;
  customCSS: string;
}

export interface Intervals {
  lastUpdated: number;
  items: Interval[];
}

export interface IntervalUpdate {
  name: string;
  description: string;
  intervals: Intervals;
}

export interface SessionNew {
  sessionId: string;
}

export interface Session extends SessionNew, IntervalUpdate {
  timer: TimerState;
  users: UsersList;
}

export interface SessionInternal extends Session {
  timerInstance: TimerCore;
  users: UsersListInternal;
  createdAt: number;
  lastActivity: number;
  emptyAt: number | null;
}

export interface SessionCreated extends SessionNew {
  clientId: string;
}

export interface SessionJoined extends SessionCreated {
  session: SessionInternal;
}

export interface WebSocketMessage {
  type: string;
}

export interface JoinSessionMessage extends WebSocketMessage {
  type: 'session_join';
  sessionId: string;
  user: User;
}

export interface SessionUpdateMessage extends WebSocketMessage {
  type: 'session_update';
  session: IntervalUpdate;
}

export interface TimerUpdateMessage extends WebSocketMessage {
  type: 'timer_update';
  timer: TimerState;
}

export interface UserUpdateMessage extends WebSocketMessage {
  type: 'user_update';
  user: User;
}

export interface GetConnectedUsersMessage extends WebSocketMessage {
  type: 'user_list';
}

export interface PingMessage extends WebSocketMessage {
  type: 'ping';
}

export interface UnknownIncomingMessage extends WebSocketMessage {
  type: 'unknown';
  originalType: unknown;
  data: unknown;
}

export type IncomingMessage =
  | JoinSessionMessage
  | SessionUpdateMessage
  | TimerUpdateMessage
  | UserUpdateMessage
  | GetConnectedUsersMessage
  | PingMessage;

export interface SessionCreatedMessage extends WebSocketMessage {
  type: 'session_created';
  sessionId: string;
  clientId: string;
}

export interface SessionJoinedMessage extends WebSocketMessage {
  type: 'session_joined';
  sessionId: string;
  clientId: string;
  session: Session;
}

export interface SessionUpdatedMessage extends WebSocketMessage {
  type: 'session_updated';
  sessionId: string;
  session: IntervalUpdate;
}

export interface TimerUpdatedMessage extends WebSocketMessage {
  type: 'timer_updated';
  sessionId: string;
  timer: TimerState;
}

export interface UserConnectedMessage extends WebSocketMessage {
  type: 'user_connected';
  sessionId: string;
  user: User;
}

export interface UserDisconnectedMessage extends WebSocketMessage {
  type: 'user_disconnected';
  sessionId: string;
  user: User;
}

export interface UserUpdatedMessage extends WebSocketMessage {
  type: 'user_updated';
  sessionId: string;
  user: User;
}

export interface ConnectedUsersMessage extends WebSocketMessage {
  type: 'connected_users';
  sessionId: string;
  users: UsersList;
}

export interface PongMessage extends WebSocketMessage {
  type: 'pong';
}

export interface ErrorMessage extends WebSocketMessage {
  type: 'error';
  message: string | number | object | null;
}

export type OutgoingMessage =
  | SessionCreatedMessage
  | SessionJoinedMessage
  | SessionUpdatedMessage
  | TimerUpdatedMessage
  | UserConnectedMessage
  | UserDisconnectedMessage
  | UserUpdatedMessage
  | ConnectedUsersMessage
  | PongMessage
  | ErrorMessage;
