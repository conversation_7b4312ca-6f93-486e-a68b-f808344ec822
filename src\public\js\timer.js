import Utils, { DOM, Events } from './utils.js';
import TimerCore from './shared/timer-core.js';
import { DEFAULT_DURATION } from './shared/constants.js';

/**
 * Timer manager class
 */
class Timer {
  /**
   * Create a new Timer instance.
   * @param {Object} app - Main application instance
   */
  constructor(app) {
    this.app = app;

    this.intervalId = null;
    this.timerCore = null;

    this.$intervalName = DOM.getId('interval-name');
    this.$intervalStatus = DOM.getId('interval-status');
    this.$startBtn = DOM.getId('start-btn');
    this.$pauseBtn = DOM.getId('pause-btn');
    this.$stopBtn = DOM.getId('stop-btn');
    this.$nextBtn = DOM.getId('next-btn');
    this.$timerDisplay = DOM.getId('timer-text');
    this.$repeatBtn = DOM.getId('repeat-btn');
    this.$repeatLine = DOM.getId('repeat-disabled-line');

    this._initialize();
  }

  /**
   * Set up timer controls and initial display.
   */
  _initialize() {
    this._listeners();
  }

  /**
   * Configure event listeners for timer control buttons.
   */
  _listeners() {
    Events.on(this.$startBtn, 'click', this.start.bind(this));
    Events.on(this.$pauseBtn, 'click', this.pause.bind(this));
    Events.on(this.$stopBtn, 'click', this.stop.bind(this));
    Events.on(this.$nextBtn, 'click', this.next.bind(this));
    Events.on(this.$repeatBtn, 'click', this.repeat.bind(this));
    Events.on(document, 'appRestarted', this.render.bind(this));
  }

  /**
   * Begin timer countdown and sync with server.
   */
  start() {
    const session = this.app.getCurrentSession();
    if (!session || !this.timerCore) {
      console.warn('No active session');
      return;
    }

    session.timer = this.timerCore.start();

    this.app.saveCurrentSession();

    if (this.app.socket.isConnected()) {
      this.app.socket.startTimer();
    }

    this.startTimer();
    this.render();
  }

  /**
   * Pause timer countdown and sync with server.
   */
  pause() {
    const session = this.app.getCurrentSession();
    if (!session || !this.timerCore) {
      console.warn('No active session');
      return;
    }

    session.timer = this.timerCore.pause();

    this.app.saveCurrentSession();

    if (this.app.socket.isConnected()) {
      this.app.socket.pauseTimer();
    }

    this.stopTimer();
    this.render();
  }

  /**
   * Stop timer and reset to initial state.
   */
  stop() {
    const session = this.app.getCurrentSession();
    if (!session || !this.timerCore) {
      console.warn('No active session');
      return;
    }

    session.timer = this.timerCore.stop();

    this.app.saveCurrentSession();

    if (this.app.socket.isConnected()) {
      this.app.socket.stopTimer();
    }

    this.stopTimer();
    this.render();
  }

  /**
   * Advance to next timer interval.
   */
  next() {
    const session = this.app.getCurrentSession();
    if (!session || !this.timerCore) {
      console.warn('No active session');
      return;
    }

    session.timer = this.timerCore.next();

    this.app.saveCurrentSession();

    if (this.app.socket.isConnected()) {
      this.app.socket.nextInterval();
    }

    this.render();
  }

  /**
   * Toggle timer repeat mode setting.
   */
  repeat() {
    const session = this.app.getCurrentSession();
    if (!session || !this.timerCore) {
      console.warn('No active session');
      return;
    }

    session.timer = this.timerCore.repeat(!session.timer.repeat);

    this.app.saveCurrentSession();

    if (this.app.socket.isConnected()) {
      this.app.socket.timerRepeat(session.timer.repeat);
    }

    this.renderState();
    this.renderRepeat();
  }

  /**
   * Update session timer state from TimerCore.
   */
  sync() {
    const session = this.app.getCurrentSession();
    if (!session || !this.timerCore) {
      console.warn('No active session');
      return;
    }

    session.timer = this.timerCore.sync();
  }

  /**
   * Begin periodic timer display updates.
   */
  startTimer() {
    this.stopTimer();
    this.intervalId = setInterval(() => {
      this.tickTimer();
    }, 200);
  }

  /**
   * Stop periodic timer display updates.
   */
  stopTimer() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Toggle timer start or pause state.
   */
  toggleTimer() {
    if (!this.timerCore) {
      console.warn('TimerCore not initialized');
      return;
    }

    const state = this.timerCore.getState();

    if (!state.isRunning || state.isPaused) {
      this.start();
    } else {
      this.pause();
    }
  }

  /**
   * Process timer tick and handle interval transitions.
   */
  tickTimer() {
    const session = this.app.getCurrentSession();
    if (!session || !this.timerCore) {
      console.warn('No active session');
      return;
    }

    const oldInterval = session.timer.currentInterval;

    this.sync();

    if (oldInterval !== session.timer.currentInterval) {
      const interval = session.intervals.items[oldInterval];
      this.app.alerts.play(interval.alert);

      session.timer = this.timerCore.getState();
      this.app.saveCurrentSession();

      this.render();
    } else {
      this.renderTimer();
      this.renderState();
    }
  }

  /**
   * Render timer HTML and update display based on current data.
   * @returns {Promise<void>} Resolves when rendering is complete
   */
  render() {
    this.renderCSS();
    this.renderTimer();
    this.renderState();
    this.renderRepeat();
  }

  /**
   * Apply custom CSS for the current interval.
   */
  renderCSS() {
    const interval = this.getCurrentInterval();
    if (interval && interval.customCSS) {
      Utils.applyCustomCSS(interval.customCSS);
    } else {
      Utils.applyCustomCSS('');
    }
  }

  /**
   * Update timer display elements with current timer and interval info.
   */
  renderTimer() {
    const session = this.app.getCurrentSession();

    if (!session) {
      this.$timerDisplay.textContent = '25:00';
      this.$intervalName.textContent = 'Focus';
      this.$intervalStatus.textContent = '1/2';
      return;
    }

    this.$timerDisplay.textContent = Utils.formatTime(Math.ceil(session.timer.timeRemaining / 1000));

    const interval = this.getCurrentInterval();
    if (interval) this.$intervalName.textContent = interval.name;

    const current = session.timer.currentInterval + 1;
    const total = session.intervals.items.length;
    this.$intervalStatus.textContent = `${current}/${total}`;

    const isRunning = session.timer.isRunning;
    const isPaused = session.timer.isPaused;

    this.$startBtn.style.display = !isRunning || isPaused ? 'flex' : 'none';
    this.$pauseBtn.style.display = isRunning && !isPaused ? 'flex' : 'none';
    this.$nextBtn.style.display = 'flex';
  }

  /**
   * Update timer state classes on the document body based on timer state.
   */
  renderState() {
    const session = this.app.getCurrentSession();
    const body = document.body;

    body.classList.remove('timer-running', 'timer-paused', 'timer-stopped', 'timer-repeat');

    if (!session) {
      body.classList.add('timer-stopped');
      return;
    }

    const isRunning = session.timer.isRunning;
    const isPaused = session.timer.isPaused;

    if (isRunning && !isPaused) {
      body.classList.add('timer-running');
    } else if (isRunning && isPaused) {
      body.classList.add('timer-running', 'timer-paused');
    } else {
      body.classList.add('timer-stopped');
    }

    if (session.timer.repeat) {
      body.classList.add('timer-repeat');
    }
  }

  /**
   * Update repeat mode button display based on current repeat state.
   */
  renderRepeat() {
    const session = this.app.getCurrentSession();
    const repeat = session ? session.timer.repeat : false;
    this.$repeatBtn.title = repeat ? 'Click to disable repeat mode' : 'Click to enable repeat mode';
    this.$repeatLine.style.display = repeat ? 'none' : 'block';
  }

  /**
   * Load session data and initialize timer for the given session ID.
   */
  reload() {
    const session = this.app.getCurrentSession();
    if (!session) {
      console.warn('No active session');
      return;
    }

    this.timerCore = new TimerCore(session.intervals.items);
    this.timerCore.updateFromSource(session.timer);

    this.render();
  }

  /**
   * Get the current timer state object.
   * @returns {Object} The current timer state
   */
  getTimerState() {
    if (!this.timerCore) {
      return {
        isRunning: false,
        isPaused: false,
        currentInterval: 0,
        timeRemaining: DEFAULT_DURATION * 1000,
        repeat: false,
        startedInterval: 0,
        startedAt: 0,
        pausedAt: 0,
        timePaused: 0,
      };
    }

    return this.timerCore.getState();
  }

  /**
   * Get the current interval object from the session.
   * @returns {Object|null} The current interval object, or null if unavailable
   */
  getCurrentInterval() {
    const session = this.app.getCurrentSession();
    if (!session) return null;

    if (!session.intervals?.items) {
      console.warn('No intervals in session');
      return null;
    }

    if (!this.timerCore) {
      console.warn('TimerCore not initialized');
      return null;
    }

    const state = this.timerCore.getState();
    const index = state.currentInterval;

    return session.intervals.items[index] || null;
  }

  /**
   * Update the duration of a interval and adjust timer state if needed.
   * @param {number} index - Index of the interval to update
   * @param {number} duration - New duration for the interval in seconds
   */
  durationUpdated(index, duration) {
    const session = this.app.getCurrentSession();
    if (!session) {
      console.warn('No active session');
      return;
    }

    if (!this.timerCore) {
      console.warn('TimerCore not initialized');
      return;
    }

    if (session.intervals.items[index]) {
      session.intervals.items[index].duration = duration;
    } else {
      console.warn('Interval not found');
      return;
    }

    this.timerCore.updateIntervals(session.intervals.items);
    session.timer = this.timerCore.getState();
  }

  /**
   * Handle deletion of a interval by updating the timer core with the new intervals list.
   */
  intervalDeleted() {
    const session = this.app.getCurrentSession();
    if (!session) {
      console.warn('No active session');
      return;
    }

    if (!this.timerCore) {
      console.warn('TimerCore not initialized');
      return;
    }

    this.timerCore.updateIntervals(session.intervals.items);
    session.timer = this.timerCore.getState();
  }

  /**
   * Dispose timer resources and clear intervals.
   */
  dispose() {
    this.stopTimer();
    this.timerCore = null;
  }
}

export default Timer;
